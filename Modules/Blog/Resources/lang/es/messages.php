<?php

return [

    'posts' => [
        'created' =>'La publicación se ha creado correctamente.',
        'saved' => 'La publicación se ha guardado correctamente.',
        'deleted' => 'La publicación ha sido eliminada con éxito.',
        'multi_deleted' => 'Las publicaciones seleccionadas se han eliminado correctamente.',

        'created_failed' => 'No se ha creado la publicación.',
        'saved_failed' => 'La publicación no ha sido guardada.',
        'deleted_failed' => 'La publicación no ha sido eliminada.',
        'multi_deleted_failed' => 'Las publicaciones seleccionadas no han sido eliminadas.',

        'not_found' => 'Publicación no encontrada.',
        'dont_have_the_permissions' => "No tienes permiso para ver esta página"
    ],


    'categories' => [
        'created' => 'La categoría se ha creado correctamente.',
        'saved' => 'La categoría se ha guardado correctamente.',
        'deleted' => 'La categoría ha sido eliminada con éxito.',
        'multi_deleted' => 'Las categorías seleccionadas se han eliminado correctamente.',

        'created_failed' => 'La categoría no ha sido creada.',
        'saved_failed' => 'La categoría no se ha guardado.',
        'deleted_failed' => 'La categoría no ha sido eliminada.',
        'multi_deleted_failed' => 'Las categorías seleccionadas no han sido eliminadas.',

        'deleted_failed_related' => 'No puedes eliminar esta categoría porque está relacionada con otros modelos.',
        'multi_deleted_failed_related' => 'No puede eliminar estas categorías porque está relacionado con otros modelos.',

        'not_found' => 'Categoría no encontrada.'
    ],

    'tags' => [
        'created' => 'La etiqueta se ha creado correctamente.',
        'saved' => 'La etiqueta se ha guardado correctamente.',
        'deleted' => 'La etiqueta se eliminó con éxito.',
        'multi_deleted' => 'Las etiquetas seleccionadas se han eliminado correctamente.',

        'created_failed' => 'No se ha creado la etiqueta.',
        'saved_failed' => 'La etiqueta no se ha guardado.',
        'deleted_failed' => 'La etiqueta no ha sido eliminada.',
        'multi_deleted_failed' => 'Las etiquetas seleccionadas no se han eliminado.',

        'deleted_failed_related' => 'No puedes eliminar esta etiqueta porque está relacionada con otros modelos.',
        'multi_deleted_failed_related' => 'No puedes eliminar estas etiquetas porque están relacionadas con otros modelos.',


        'not_found' => 'Etiqueta no encontrada.'
    ],


    'comments' => [
        'created' => 'El comentario se ha creado correctamente.',
        'saved' => 'El comentario se ha guardado correctamente.',
        'deleted' => 'El comentario ha sido eliminado con éxito.',
        'multi_deleted' => 'Los comentarios seleccionados se han eliminado correctamente.',

        'rejected' => 'El comentario ha sido rechazado con éxito.',
        'multi_rejected' => 'Los comentarios seleccionados han sido rechazados con éxito.',

        'approved' => 'El comentario ha sido aprobado con éxito.',
        'multi_approved' => 'Los comentarios seleccionados han sido aprobados con éxito.',

        'created_failed' => 'No se ha creado el comentario.',
        'saved_failed' => 'El comentario no se ha guardado.',
        'deleted_failed' => 'El comentario no ha sido eliminado.',
        'multi_deleted_failed' => 'Los comentarios seleccionados no han sido eliminados.',

        'rejected_failed' => 'El comentario no ha sido rechazado.',
        'multi_rejected_failed' => 'Los comentarios seleccionados no han sido rechazados.',

        'approved_failed' => 'El comentario no ha sido aprobado.',
        'multi_approved_failed' => 'Los comentarios seleccionados no han sido aprobados.',

        'deleted_failed_related' => 'No puedes eliminar este comentario porque está relacionado con otros modelos.',
        'multi_deleted_failed_related' => 'No puedes borrar estos comentarios porque está relacionado con otros modelos.',


        'not_found' => 'Comentario no encontrado.',
        'not_found_post' => 'Publicación no encontrada.',
    ],




];