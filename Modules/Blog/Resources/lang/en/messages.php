<?php

return [

    'posts' => [
        'created' => 'Post has been created successfully.',
        'saved' => 'Post has been saved successfully.',
        'deleted' => 'Post has been deleted successfully.',
        'multi_deleted' => 'Posts selected has been deleted successfully.',
        
        'created_failed' => 'Post has been not created.',
        'saved_failed' => 'Post has been not saved.',
        'deleted_failed' => 'Post has been not deleted.',
        'multi_deleted_failed' => 'Posts selected has been not deleted.',

        'not_found' => 'Post not found.',
        'dont_have_the_permissions' => "You don't have permission to view this page"
    ],

    
    'categories' => [
        'created' => 'Category has been created successfully.',
        'saved' => 'Category has been saved successfully.',
        'deleted' => 'Category has been deleted successfully.',
        'multi_deleted' => 'Categories selected has been deleted successfully.',
        
        'created_failed' => 'Category has been not created.',
        'saved_failed' => 'Category has been not saved.',
        'deleted_failed' => 'Category has been not deleted.',
        'multi_deleted_failed' => 'Categories selected has been not deleted.',

        'deleted_failed_related' => 'You cannot delete this category because it is related to other models.',
        'multi_deleted_failed_related' => 'You cannot delete these categories because it is related to other models.',

        'not_found' => 'Category not found.'
    ],

    'tags' => [
        'created' => 'Tag has been created successfully.',
        'saved' => 'Tag has been saved successfully.',
        'deleted' => 'Tag has been deleted successfully.',
        'multi_deleted' => 'Tags selected has been deleted successfully.',
        
        'created_failed' => 'Tag has been not created.',
        'saved_failed' => 'Tag has been not saved.',
        'deleted_failed' => 'Tag has been not deleted.',
        'multi_deleted_failed' => 'Tags selected has been not deleted.',
        
        'deleted_failed_related' => 'You cannot delete this tag because it is related to other models.',
        'multi_deleted_failed_related' => 'You cannot delete these tags because it is related to other models.',
        

        'not_found' => 'Tag not found.'
    ],


    'comments' => [
        'created' => 'Comment has been created successfully.',
        'saved' => 'Comment has been saved successfully.',
        'deleted' => 'Comment has been deleted successfully.',
        'multi_deleted' => 'Comments selected has been deleted successfully.',

        'rejected' => 'Comment has been rejected successfully.',
        'multi_rejected' => 'Comments selected has been rejected successfully.',

        'approved' => 'Comment has been approved successfully.',
        'multi_approved' => 'Comments selected has been approved successfully.',
        
        'created_failed' => 'Comment has been not created.',
        'saved_failed' => 'Comment has been not saved.',
        'deleted_failed' => 'Comment has been not deleted.',
        'multi_deleted_failed' => 'Comments selected has been not deleted.',

        'rejected_failed' => 'Comment has been not rejected.',
        'multi_rejected_failed' => 'Comments selected has been not rejected.',

        'approved_failed' => 'Comment has been not approved.',
        'multi_approved_failed' => 'Comments selected has been not approved.',
        
        'deleted_failed_related' => 'You cannot delete this comment because it is related to other models.',
        'multi_deleted_failed_related' => 'You cannot delete these comments because it is related to other models.',
        

        'not_found' => 'Comment not found.',
        'not_found_post' => 'Post not found.',
    ],


    

];