@php
    $user_role = auth()->user()->role;
    $admin  = 1;
    $branch  = 3;
@endphp

@extends('users::adminLte.layouts.master')

@section('pageTitle')
    {{ __('view.profile') }} - {{$model->name}}
@endsection

@section('content')
    
    @include('cargo::adminLte.pages.clients.overview-profile', ['model' => $model, 'shipments' => $shipments])

    <!--begin::Basic info-->

    @if ($user_role == 1 || $user_role == 3)
    @php
        $client_id = $model->id;
        $all_client_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)->count();
        $saved_client_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('status_id', Modules\Cargo\Entities\Shipment::SAVED_STATUS)
            ->count();
        $in_progress_client_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('client_status', Modules\Cargo\Entities\Shipment::CLIENT_STATUS_IN_PROCESSING)
            ->count();
        $delivered_client_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('client_status', Modules\Cargo\Entities\Shipment::CLIENT_STATUS_DELIVERED)
            ->count();

        $transactions = Modules\Cargo\Entities\Transaction::where('client_id', $client_id)
            ->orderBy('created_at', 'desc')
            ->sum('value');
        $DEBIT_transactions = Modules\Cargo\Entities\Transaction::where('client_id', $client_id)
            ->where('value', 'like', '%-%')
            ->orderBy('created_at', 'desc')
            ->sum('value');
        $CREDIT_transactions = Modules\Cargo\Entities\Transaction::where('client_id', $client_id)
            ->where('value', 'not like', '%-%')
            ->orderBy('created_at', 'desc')
            ->sum('value');

        // DEBIT  -
        // CREDIT  +

        $deleverd_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('is_withdraw', 0)
            ->where('status_id', 7) // 7 is delivered
            ->where('type', 1)
            ->selectRaw('SUM(amount_to_be_collected) as total_collected, SUM(shipping_cost) as total_shipping')
            ->first();

        $supplied_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('is_withdraw', 0)
            ->whereIn('status_id', [10, 11]) // 10 is supplied as return from reciever and 11 is return to provider
            ->where('type', 3)
            ->selectRaw('SUM(amount_to_be_collected) as total_collected, SUM(shipping_cost) as total_shipping')
            ->first();

        $returned_shipments = Modules\Cargo\Entities\Shipment::where('client_id', $client_id)
            ->where('is_withdraw', 0)
            ->where('status_id', 11) // 11 is return to provider
            ->where('type', 1)
            ->selectRaw('SUM(amount_to_be_collected) as total_collected, SUM(return_cost) as total_return_cost')
            ->first();

        $deleverd_cost = $deleverd_shipments->total_collected - $deleverd_shipments->total_shipping; //+

        $supplied_cost = $supplied_shipments->total_collected + $supplied_shipments->total_shipping; // -

        $returned_cost = $returned_shipments->total_return_cost; // -

        // $amount_to_be_collected = $shipmentData->total_collected ?? 0;
        // $shipping_cost = $shipmentData->total_shipping ?? 0;

        // $total = $amount_to_be_collected - $shipping_cost;

    @endphp

    <div class="col-lg-12">
        <!--begin::Stats Widget 30-->
        <div class="card card-custom bgi-no-repeat card-stretch gutter-b">
            <!--begin::Body-->
            <div class="card-body">
                <a href="{{-- route('transactions.index') --}}"
                    class="mb-0 font-weight-bold text-light-75 text-hover-primary font-size-h5">{{ __('Client wallet') }}
                    <div class="font-weight-bold text-success mt-2">{{ format_price($deleverd_cost) }}</div>
                    <div class="font-weight-bold text-danger mt-3">{{ format_price($supplied_cost + $returned_cost) }}
                    </div>
                    <div style="width: 15%;height: 1px;background-color: #3f4254;margin-top: 9px;"></div>
                    <div class="mb-3 font-weight-bold text-success mt-4">
                        {{ format_price($deleverd_cost - ($supplied_cost + $returned_cost)) }}</div>
                </a>
                {{-- <p class="m-0 text-dark-75 font-weight-bolder font-size-h5">{{ __('cargo::view.client_wallet_dashboard') }}.</p> --}}

            </div>
            <!--end::Body-->
        </div>
        <!--end::Stats Widget 30-->
    </div>
@endif

    
    <div class="card mb-5 mb-xl-10">
        <!--begin::Card header-->
        {{-- <div class="card-header border-0 cursor-pointer" role="button" data-bs-toggle="collapse" data-bs-target="#kt_account_profile_details" aria-expanded="true" aria-controls="kt_account_profile_details"> --}}
        <div class="card-header">
            <!--begin::Card title-->
            <div class="card-title m-0">
                <h3 class="fw-bolder m-0">{{ __('view.profile_details') }}</h3>
            </div>
            <!--end::Card title-->

            @if(auth()->user()->can('edit-customers') || $user_role == $admin || $user_role == $branch)
                <a href="{{ fr_route('clients.edit', $model->id) }}" class="btn btn-primary align-self-center">{{ __('view.edit_profile') }}</a>
            @endif
        </div>
        <!--begin::Card header-->
        <div class="card-body p-9">

                <!--begin::Row  Full name -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('users::view.table.full_name') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->name }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  Email -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('users::view.table.email') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->email }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  Owner Name -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('cargo::view.table.owner_name') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->responsible_name }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  Owner Phone -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('cargo::view.table.owner_phone') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->responsible_mobile }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  Address -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('cargo::view.table.address') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->address }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  National ID -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('cargo::view.table.national_id') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                        <span class="fw-bolder fs-6 text-gray-800">{{ $model->national_id }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Row  Branch -->
                <div class="row mb-7">
                    <!--begin::Label-->
                    <label class="col-lg-4 fw-bold text-muted">{{ __('cargo::view.table.branch') }}</label>
                    <!--end::Label-->
                    <!--begin::Col-->
                    <div class="col-lg-8">
                    <span class="fw-bolder fs-6 text-gray-800">{{ $model->branch->name }}</span>
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->
        </div>
        <!--begin::Card header-->
    </div>
    <!--end::Basic info-->

@endsection