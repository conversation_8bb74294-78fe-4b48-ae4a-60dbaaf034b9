@csrf

@php
    $user_role = auth()->user()->role;
    $admin  = 1;

    $is_def_mile_or_fees = Modules\Cargo\Entities\ShipmentSetting::getVal('is_def_mile_or_fees');

    $googleSettings = resolve(\app\Models\GoogleSettings::class)->toArray();
    $googleMap = json_decode($googleSettings['google_map'], true);
    $google_map_key = '';
    if($googleMap){
        $google_map_key = $googleMap['google_map_key'];
    }

    $countries = Modules\Cargo\Entities\Country::where('covered',1)->get();
@endphp


<!--begin::Col Avatar -->
<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.table.avatar') }}</label>
    <!--end::Label-->
    <div class="col-md-12">
        <!--begin::Image input-->
        @if(isset($model))
            <x-media-library-collection max-items="1" name="image" :model="$model" collection="avatar" rules="mimes:jpg,jpeg,png,gif,bmp,svg,webp"/>
        @else
            <x-media-library-attachment name="image" rules="mimes:jpg,jpeg,png,gif,bmp,svg,webp"/>
        @endif
        <!--end::Image input-->

        @error('avatar')
            <div class="is-invalid"></div>
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror

    </div>
</div>
<!--end::Col-->

<!--begin::Input group -- Branch -->
<div class="row mb-6">

    <!--begin::Input group-->
    <div class="fv-row col-lg-12 form-group">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('Status') }}</label>
        <!--end::Label-->
        <select required
            class="form-control  @error('status') is-invalid @enderror"
            name="status"
            data-control="select2"
            data-placeholder="{{ __('Status') }}"
            data-allow-clear="true"
        >
                <option value="approved"
                    {{ old('status') == 'approved' ? 'selected' : '' }}
                    @if($typeForm == 'edit')
                        {{ $model->status == 'approved' ? 'selected' : '' }}
                    @endif
                >Approved</option>
                <option value="not_approved"
                    {{ old('status') == 'not_approved' ? 'selected' : '' }}
                    @if($typeForm == 'edit')
                        {{ $model->status == 'not_approved' ? 'selected' : '' }}
                    @endif
                >Not Approved</option>

        </select>
        @error('status')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->


<!--begin::Input group --  Full name -->
<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.full_name') }}</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <input type="text" name="name" class="form-control form-control-lg @error('name') is-invalid @enderror" placeholder="{{ __('cargo::view.table.full_name') }}" value="{{ old('name', isset($model) ? $model->name : '') }}" />
            @error('name')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<!--begin::Input group --  Email -->
<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.email') }}</label>
    <!--end::Label-->
    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <input type="text" name="email" class="form-control form-control-lg @error('email') is-invalid @enderror" placeholder="{{ __('cargo::view.table.email') }}" value="{{ old('email', isset($model) ? $model->email : '') }}" />
            @error('email')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->


<div class="row mb-6">

    <!--begin::Input group --  Password -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 @if($typeForm == 'create') required @endif">{{ __('cargo::view.table.password') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="password" id="password" name="password" class="form-control form-control-lg has-feedback @error('password') is-invalid @enderror" placeholder="{{ __('cargo::view.table.password') }}"  />
            <i id="check" class="far fa-eye" id="togglePassword" style="cursor: pointer;position: absolute;right: 0;padding: 3%;font-size: 16px;"></i>
            @error('password')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->


    <!--begin::Input group --  National Id -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.owner_national_id') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="number" name="national_id" class="form-control form-control-lg @error('national_id') is-invalid @enderror" placeholder="{{ __('cargo::view.table.owner_national_id') }}" value="{{ old('national_id', isset($model) ? $model->national_id : '') }}" />
            @error('national_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<div class="row mb-6">

    <!--begin::Input group --  Owner Name -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.owner_name') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="text" name="responsible_name" class="form-control form-control-lg @error('responsible_name') is-invalid @enderror" placeholder="{{ __('cargo::view.table.owner_name') }}" value="{{ old('responsible_name', isset($model) ? $model->responsible_name : '') }}" />
            @error('responsible_name')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->


    <!--begin::Input group --  Owner Phone -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.owner_phone') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="tel" name="responsible_mobile" class=" phone_input phone_input_1  number-only  form-control form-control-lg @error('responsible_mobile') is-invalid @enderror" placeholder="{{ __('cargo::view.table.owner_phone') }}" value="{{ old('responsible_mobile', isset($model) ? $model->country_code.$model->responsible_mobile : base_country_code()) }}" dir="ltr" autocomplete="off" required   />
            <input type="hidden" class="country_code country_code_1" name="country_code" value="{{ old('country_code', isset($model) ? $model->country_code : base_country_code()) }}" data-reflection="phone">
            @error('responsible_mobile')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<div class="row mb-6">

    <!--begin::Input group --  follow_up_name -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.table.follow_up_name') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="text" name="follow_up_name" class="form-control form-control-lg @error('follow_up_name') is-invalid @enderror" placeholder="{{ __('cargo::view.table.follow_up_name') }}" value="{{ old('follow_up_name', isset($model) ? $model->follow_up_name : '') }}" />
            @error('follow_up_name')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->


    <!--begin::Input group --  follow_up_mobile -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.table.follow_up_mobile') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="tel" name="follow_up_mobile" class=" phone_input phone_input_2  number-only form-control form-control-lg @error('follow_up_mobile') is-invalid @enderror" placeholder="{{ __('cargo::view.table.follow_up_mobile') }}" value="{{ old('follow_up_mobile', isset($model) ? $model->follow_up_country_code.$model->follow_up_mobile :  base_country_code()) }}" dir="ltr" autocomplete="off"  />
            <input type="hidden" class="country_code  country_code_2 " name="follow_up_country_code" value="{{ old('follow_up_country_code', isset($model) ? $model->follow_up_country_code :  base_country_code()) }}" data-reflection="phone">
            @error('follow_up_mobile')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>



    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('region') }}</label>
            <select id="change-state-to" required name="state_id"
                class="form-control select-state @error('state_id') is-invalid @enderror">
                <option value=""></option>
                    @foreach (Modules\Cargo\Entities\State::where('country_id', 65 )->where('covered', 1)->get() as $item)
                        <option value="{{ $item->id }}"
                            @if ($typeForm == 'edit') {{ $model->state_id == $item->id ? 'selected' : '' }} @endif>
                            {{ $item->name }}</option>
                    @endforeach
            </select>
            @error('state_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('area') }}</label>
            <select name="area_id"  required
                class="form-control select-area @error('area_id') is-invalid @enderror">
                <option value=""></option>
                @if ($typeForm == 'edit')
                    @foreach (Modules\Cargo\Entities\Area::where('state_id', $model->state_id)->get() as $item)
                        <option value="{{ $item->id }}"
                            @if ($typeForm == 'edit') {{ $model->area_id == $item->id ? 'selected' : '' }} @endif>
                            {{ json_decode($item->name, true)[app()->getLocale()] }}</option>
                    @endforeach
                @endif
            </select>
            @error('area_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>





    <!--end::Input group-->
</div>

<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">Payment Details</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <textarea required  class="w-100" placeholder="Comment" id="comment_content" name="payment_details" rows="8" maxlength="65000"> @if ($typeForm == 'edit') {{ $model->payment_details }} @endif  </textarea>
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<!--begin::Input group -- Branch -->
<div class="row mb-6">

    <!--begin::Input group-->
    <div class="fv-row col-lg-12 form-group">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.branch') }}</label>
        <!--end::Label-->
        <select
            class="form-control  @error('branch_id') is-invalid @enderror"
            name="branch_id"
            data-control="select2"
            data-placeholder="{{ __('cargo::view.table.choose_branch') }}"
            data-allow-clear="true"
        >
            <option></option>
            @foreach($branches as $branch)
                <option value="{{ $branch->id }}"
                    {{ old('branch_id') == $branch->id ? 'selected' : '' }}
                    @if($typeForm == 'edit')
                        {{ $model->branch_id == $branch->id ? 'selected' : '' }}
                    @endif
                >{{ $branch->name }}</option>
            @endforeach
        </select>
        @error('branch_id')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<div class="form-group" id="kt_repeater_2">
    <div data-repeater-list="address">
        @if($typeForm == 'create')
            <div data-repeater-item class="data-repeater-item-count">

                {{-- <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.country') }}</label>
                            <select name="country_id" class="change-country-client-address form-control select-country @error('country_id') is-invalid @enderror">
                                <option value=""></option>
                                @foreach($countries as $country)
                                    <option value="{{$country->id}}">{{$country->name}}</option>
                                @endforeach
                            </select>
                            @error('country_id')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.region') }}</label>
                            <select @error('state_id') is-invalid @enderror name="state_id" class="change-state-client-address form-control select-state">
                                <option value=""></option>

                            </select>
                            @error('state_id')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.area') }}</label>
                            <select @error('area_id') is-invalid @enderror name="area_id" style="display: block !important;" class="change-area-client-address form-control select-area">
                                <option value=""></option>

                            </select>
                            @error('area_id')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>
                    </div>
                </div> --}}

                <div class="form-group">
                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.address') }}</label>
                    <input @error('address') is-invalid @enderror type="text" placeholder="{{ __('cargo::view.address') }}" name="address" class="form-control" />
                    @error('address')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>

                @if($googleMap)
                    <div class="mt-2 location-client">
                        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.location') }}</label>
                        <input type="text" class="form-control address address-client " placeholder="{{ __('cargo::view.location') }}" name="client_street_address_map"  rel="client" value="" />
                        <input type="hidden" class="form-control lat" data-client="lat" name="client_lat" />
                        <input type="hidden" class="form-control lng" data-client="lng" name="client_lng" />
                        <input type="hidden" class="form-control url" data-client="url" name="client_url" />

                        <div class="mt-2 col-sm-12 map_canvas map-client" style="width:100%;height:300px;"></div>
                        <span class="form-text text-muted">{{'Change the pin to select the right location'}}</span>
                    </div>
                @endif

                <div class="mt-3 mb-3 row">
                    <div class="col-md-12">
                        <a href="javascript:;" data-repeater-delete="" class="btn btn-sm font-weight-bolder btn-light-danger delete_item">
                            <i class="la la-trash-o"></i>{{ __('cargo::view.delete') }}
                        </a>
                    </div>
                </div>
            </div>
        @elseif($typeForm == 'edit')
            @if($model)
                @forelse($model->addressess as $address)
                    @php
                        $states = array();
                        $areas  = array();
                    @endphp
                    <div data-repeater-item class="data-repeater-item-count">

                        {{-- <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.country') }}</label>
                                    <select name="country_id" class="change-country-client-address form-control select-country @error('country_id') is-invalid @enderror">
                                        <option value=""></option>
                                        @foreach($countries as $country)
                                            <option value="{{$country->id}}" @if($country->id == $address->country_id ) selected @endif >{{$country->name}}</option>
                                            @php
                                                if($country->id == $address->country_id )
                                                $states = Modules\Cargo\Entities\State::where('country_id',$address->country_id)->get();
                                            @endphp
                                        @endforeach
                                    </select>
                                    @error('country_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.region') }}</label>
                                    <select @error('state_id') is-invalid @enderror name="state_id" class="change-state-client-address form-control select-state">
                                        <option value=""></option>
                                        @foreach($states as $state)
                                            <option value="{{$state->id}}" @if($state->id == $address->state_id ) selected @endif >{{$state->name}}</option>
                                            @php
                                                if($state->id == $address->state_id )
                                                $areas = Modules\Cargo\Entities\Area::where('state_id',$address->state_id)->get();
                                            @endphp
                                        @endforeach

                                    </select>
                                    @error('state_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.area') }}</label>
                                    <select @error('area_id') is-invalid @enderror name="area_id" style="display: block !important;" class="change-area-client-address form-control select-area">
                                        <option value=""></option>
                                        @foreach($areas as $area)
                                            <option value="{{$area->id}}" @if($area->id == $address->area_id ) selected @endif >{{json_decode($area->name, true)[app()->getLocale()]}}</option>
                                        @endforeach
                                    </select>
                                    @error('area_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div>
                        </div> --}}

                        <div class="form-group">
                            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.address') }}</label>
                            <input @error('address') is-invalid @enderror value="{{$address->address}}" type="text" placeholder="{{ __('cargo::view.address') }}" name="address" class="form-control" />
                            @error('address')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        @if($googleMap)
                            <div class="mt-2 location-client location-client-{{$address->id}}">
                                <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.location') }}</label>
                                <input type="text" value="{{$address->client_street_address_map}}" class="form-control address address-client-{{$address->id}} " placeholder="{{ __('cargo::view.location') }}" name="client_street_address_map"  rel="client"/>
                                <input type="hidden" value="{{$address->client_lat}}" class="form-control lat" data-client="lat" name="client_lat" />
                                <input type="hidden" value="{{$address->client_lng}}" class="form-control lng" data-client="lng" name="client_lng" />
                                <input type="hidden" value="{{$address->client_url}}" class="form-control url" data-client="url" name="client_url" />

                                <div class="mt-2 col-sm-12 map_canvas map_canvas_{{$address->id}} map-client map-client_{{$address->id}}" style="width:100%;height:300px;"></div>
                                <span class="form-text text-muted">{{'Change the pin to select the right location'}}</span>
                            </div>
                        @endif

                        <div class="mt-3 mb-3 row">
                            <div class="col-md-12">
                                <a href="javascript:;" data-repeater-delete="" class="btn btn-sm font-weight-bolder btn-light-danger delete_item">
                                    <i class="la la-trash-o"></i>{{ __('cargo::view.delete') }}
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div data-repeater-item class="data-repeater-item-count">

                        <div class="row">
                            {{-- <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.country') }}</label>
                                    <select name="country_id" class="change-country-client-address form-control select-country @error('country_id') is-invalid @enderror">
                                        <option value=""></option>
                                        @foreach($countries as $country)
                                            <option value="{{$country->id}}">{{$country->name}}</option>
                                        @endforeach
                                    </select>
                                    @error('country_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div> --}}
                            {{-- <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.region') }}</label>
                                    <select @error('state_id') is-invalid @enderror name="state_id" class="change-state-client-address form-control select-state">
                                        <option value=""></option>

                                    </select>
                                    @error('state_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div> --}}
                            {{-- <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.area') }}</label>
                                    <select @error('area_id') is-invalid @enderror name="area_id" style="display: block !important;" class="change-area-client-address form-control select-area">
                                        <option value=""></option>

                                    </select>
                                    @error('area_id')
                                        <div class="invalid-feedback">
                                            {{ $message }}
                                        </div>
                                    @enderror
                                </div>
                            </div> --}}
                        </div>

                        <div class="form-group">
                            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.address') }}</label>
                            <input @error('address') is-invalid @enderror type="text" placeholder="{{ __('cargo::view.address') }}" name="address" class="form-control" />
                            @error('address')
                                <div class="invalid-feedback">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        @if($googleMap)
                            <div class="mt-2 location-client">
                                <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.location') }}</label>
                                <input type="text" class="form-control address address-client " placeholder="{{ __('cargo::view.location') }}" name="client_street_address_map"  rel="client" value="" />
                                <input type="hidden" class="form-control lat" data-client="lat" name="client_lat" />
                                <input type="hidden" class="form-control lng" data-client="lng" name="client_lng" />
                                <input type="hidden" class="form-control url" data-client="url" name="client_url" />

                                <div class="mt-2 col-sm-12 map_canvas map-client" style="width:100%;height:300px;"></div>
                                <span class="form-text text-muted">{{'Change the pin to select the right location'}}</span>
                            </div>
                        @endif

                        <div class="mt-3 mb-3 row">
                            <div class="col-md-12">
                                <a href="javascript:;" data-repeater-delete="" class="btn btn-sm font-weight-bolder btn-light-danger delete_item">
                                    <i class="la la-trash-o"></i>{{ __('cargo::view.delete') }}
                                </a>
                            </div>
                        </div>
                    </div>
                @endforelse
            @endif
        @endif
    </div>

    {{-- <div class="form-group row">
        <div class="col-md-12">
            <div>
                <a href="javascript:;" data-repeater-create="" class="btn btn-sm font-weight-bolder btn-light-primary">
                    <i class="la la-plus"></i>{{ __('cargo::view.add_new_address') }}
                </a>
            </div>
        </div>
    </div> --}}
</div>

<!--begin::Input group -- How Know Us -->
<div class="row mb-6">

    <!--begin::Input group-->
    <div class="fv-row col-lg-12 form-group">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.table.customer_source') }}</label>
        <!--end::Label-->

        <select
            class="form-control  @error('how_know_us') is-invalid @enderror"
            name="how_know_us"
            data-control="select2"
            data-placeholder="{{ __('cargo::view.table.customer_source') }}"
            data-allow-clear="true"
        >
            <option></option>
            <option
                {{ old('how_know_us') ==  'Facebook' ? 'selected' : '' }}
                @if($typeForm == 'edit')
                    {{ $model->how_know_us ==  "Facebook" ? 'selected' : '' }}
                @endif
                value="Facebook">{{ __('cargo::view.facebook') }}
            </option>
            <option
                {{ old('how_know_us') ==  'Website' ? 'selected' : '' }}
                @if($typeForm == 'edit')
                    {{ $model->how_know_us ==  "Website" ? 'selected' : '' }}
                @endif
                value="Website">{{ __('cargo::view.website') }}
            </option>
            <option
                {{ old('how_know_us') ==  'Friend' ? 'selected' : '' }}
                @if($typeForm == 'edit')
                    {{ $model->how_know_us ==  "Friend" ? 'selected' : '' }}
                @endif
                value="Friend">{{ __('cargo::view.friend') }}
            </option>
            <option
                {{ old('how_know_us') ==  'Sales Team' ? 'selected' : '' }}
                @if($typeForm == 'edit')
                    {{ $model->how_know_us ==  "Sales Team" ? 'selected' : '' }}
                @endif
                value="Sales Team">{{ __('cargo::view.sales_team') }}
            </option>
            <option
                {{ old('how_know_us') ==  'Google' ? 'selected' : '' }}
                @if($typeForm == 'edit')
                    {{ $model->how_know_us ==  "Google" ? 'selected' : '' }}
                @endif
                value="Google">{{ __('cargo::view.google') }}
            </option>
        </select>
        @error('how_know_us')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<!--begin::Input group --  Missions Costs -->
<div hidden class="row mb-6">

    <div class="col-lg-12 card-header">
        <h5>{{ __('cargo::view.missions_costs') }}</h5>
    </div>

    <!--begin::Input group --  pickup_cost -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.custom_pickup_cost') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="number" min="0" name="pickup_cost" class="form-control form-control-lg @error('pickup_cost') is-invalid @enderror" placeholder="{{ __('cargo::view.custom_pickup_cost') }}" value="{{ old('pickup_cost', isset($model) ? $model->pickup_cost : Modules\Cargo\Entities\ShipmentSetting::getVal('def_pickup_cost') ) }}" />
            @error('pickup_cost')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->


    <!--begin::Input group --  Custom Supply Cost -->
    <!--begin::Input group-->
    <div class="col-lg-6 fv-row">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.custom_supply_cost') }}</label>
        <!--end::Label-->
        <div class="input-group mb-4">
            <input type="number" min="0" name="supply_cost" class="form-control form-control-lg @error('supply_cost') is-invalid @enderror" placeholder="{{ __('cargo::view.custom_supply_cost') }}" value="{{ old('supply_cost', isset($model) ? $model->supply_cost : Modules\Cargo\Entities\ShipmentSetting::getVal('def_supply_cost') ) }}" />
            @error('supply_cost')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->

<!--begin::Input group -- Cost Details Display -->
<div class="row mb-6" id="cost-details-section" style="display: none;">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-calculator"></i> {{ __('Shipping Cost Details') }}</h5>
                <small class="text-muted">{{ __('Select a region to see shipping costs') }}</small>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-primary">{{ __('Selected Region') }}: <span id="selected-region-name">{{ __('Please select a region') }}</span></h6>
                        <h6 class="text-secondary">{{ __('Selected Area') }}: <span id="selected-area-name">{{ __('Please select an area') }}</span></h6>
                    </div>
                    <div class="col-md-6">
                        <div class="text-end">
                            <span class="badge badge-info" id="cost-status">{{ __('Select Region') }}</span>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ __('Default Shipping Cost') }}</h6>
                                <h4 class="text-primary" id="def-shipping-cost">-</h4>
                                <small class="text-muted">{{ __('First 3kg') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ __('Default Return Cost') }}</h6>
                                <h4 class="text-success" id="def-return-cost">-</h4>
                                <small class="text-muted">{{ __('First 3kg') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ __('Shipping Cost/Kg') }}</h6>
                                <h4 class="text-warning" id="def-shipping-cost-gram">-</h4>
                                <small class="text-muted">{{ __('After 3kg') }}</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body text-center">
                                <h6 class="card-title">{{ __('Return Cost/Kg') }}</h6>
                                <h4 class="text-info" id="def-return-cost-gram">-</h4>
                                <small class="text-muted">{{ __('After 3kg') }}</small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="alert alert-light">
                            <h6><i class="fas fa-calculator"></i> {{ __('Cost Calculation Example') }}:</h6>
                            <p class="mb-0" id="cost-example">{{ __('Select a region to see cost calculation example') }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--begin::Input group -- Client State Cost Override -->
<div class="row mb-6" id="client-state-cost-override-section" style="display: none;">
    <div class="col-lg-12">
        <div class="card border-warning">
            <div class="card-header bg-warning">
                <h5 class="mb-0"><i class="fas fa-edit"></i> {{ __('Custom State Costs for This Client') }}</h5>
                <small class="text-dark">{{ __('Override default state costs with client-specific rates') }}</small>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="cost-override-state" class="form-label">{{ __('Select State to Customize') }}</label>
                        <select class="form-control" id="cost-override-state" name="cost_override_state">
                            <option value="">{{ __('Select State') }}</option>
                            @foreach (Modules\Cargo\Entities\State::where('country_id', 65)->where('covered', 1)->get() as $state)
                                <option value="{{ $state->id }}">{{ $state->name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="col-md-6">
                        <div class="mt-4">
                            <span class="badge badge-info" id="override-status">{{ __('Select State') }}</span>
                            <button type="button" class="btn btn-sm btn-success ml-2" id="save-override-btn" style="display: none;">
                                <i class="fas fa-save"></i> {{ __('Save Override') }}
                            </button>
                            <button type="button" class="btn btn-sm btn-danger ml-2" id="remove-override-btn" style="display: none;">
                                <i class="fas fa-trash"></i> {{ __('Remove Override') }}
                            </button>
                        </div>
                    </div>
                </div>

                <div class="row" id="cost-override-fields" style="display: none;">
                    <div class="col-md-3">
                        <label for="override-def-shipping-cost" class="form-label">{{ __('Default Shipping Cost') }}</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" step="0.01" class="form-control" id="override-def-shipping-cost" placeholder="0.00">
                        </div>
                        <small class="text-muted">{{ __('First 3kg') }}</small>
                    </div>
                    <div class="col-md-3">
                        <label for="override-def-return-cost" class="form-label">{{ __('Default Return Cost') }}</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" step="0.01" class="form-control" id="override-def-return-cost" placeholder="0.00">
                        </div>
                        <small class="text-muted">{{ __('First 3kg') }}</small>
                    </div>
                    <div class="col-md-3">
                        <label for="override-def-shipping-cost-gram" class="form-label">{{ __('Shipping Cost/Kg') }}</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" step="0.01" class="form-control" id="override-def-shipping-cost-gram" placeholder="0.00">
                        </div>
                        <small class="text-muted">{{ __('After 3kg') }}</small>
                    </div>
                    <div class="col-md-3">
                        <label for="override-def-return-cost-gram" class="form-label">{{ __('Return Cost/Kg') }}</label>
                        <div class="input-group">
                            <span class="input-group-text">$</span>
                            <input type="number" step="0.01" class="form-control" id="override-def-return-cost-gram" placeholder="0.00">
                        </div>
                        <small class="text-muted">{{ __('After 3kg') }}</small>
                    </div>
                </div>

                <div class="row mt-3" id="override-comparison" style="display: none;">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <h6><i class="fas fa-balance-scale"></i> {{ __('Cost Comparison') }}:</h6>
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>{{ __('Default State Costs') }}:</strong>
                                    <div id="default-costs-display"></div>
                                </div>
                                <div class="col-md-6">
                                    <strong>{{ __('Your Custom Costs') }}:</strong>
                                    <div id="custom-costs-display"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--begin::Input group -- Note about regional costs -->
<div class="row mb-6">
    <div class="col-lg-12">
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle"></i> {{ __('Regional Cost Management') }}</h5>
            <p class="mb-0">{{ __('Shipping costs are now managed per region/state. You can configure costs for each region in the') }}
               <a href="{{ route('admin.state-costs.index') }}" target="_blank">{{ __('State Costs Management') }}</a> {{ __('section.') }}
            </p>
        </div>
    </div>
</div>
<!--end::Input group-->











<!--begin::Input group --  Extra Fees for Package Types -->
<div hidden class="row mb-5">
    <div class="col-lg-12 card-header">
        <h5>{{ __('cargo::view.extra_fees_for_package_types') }}</h5>
    </div>
    <div class="col-lg-12 fv-row">
        @if(count($packages = Modules\Cargo\Entities\Package::all()))
            <table class="table mb-0 aiz-table">
                <thead>
                    <tr>
                        <th>{{ __('cargo::view.table.name') }}</th>
                        <th>{{ __('cargo::view.extra_cost') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @if($typeForm == 'create')
                        @foreach($packages as $key => $package)
                            <tr>
                                <td>{{json_decode($package->name, true)[app()->getLocale()]}}</td>
                                <td>

                                    <input type="number" min="0" name="package_extra[]" class="form-control" id="" value="{{$package->cost}}" />
                                    <input type="hidden" name="package_id[]" value="{{$package->id}}">

                                </td>
                            </tr>
                        @endforeach
                    @elseif($typeForm == 'edit')
                        @if($model)
                            @php
                                $package_ids[] = '';
                            @endphp
                            @foreach($model->packages as $key => $package)
                                @php
                                    $package_ids[] =  $package->package_id;
                                @endphp
                                <tr>
                                    <td>{{json_decode($package->name, true)[app()->getLocale()]}}</td>
                                    <td>

                                        <input type="number" min="0" name="package_extra[]" class="form-control" id="" value="{{$package->cost}}" />
                                        <input type="hidden" name="package_id[]" value="{{$package->package_id}}">

                                    </td>
                                </tr>
                            @endforeach
                            @foreach($packages = Modules\Cargo\Entities\Package::whereNotIn('id',$package_ids)->get() as $key => $package)
                                <tr>
                                    <td>{{json_decode($package->name, true)[app()->getLocale()]}}</td>
                                    <td>

                                        <input type="number" min="0" name="package_extra[]" class="form-control" id="" value="{{$package->cost}}" />
                                        <input type="hidden" name="package_id[]" value="{{$package->id}}">

                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    @endif
                </tbody>
            </table>
        @else
            <div class="alert alert-danger col-lg-8" style="margin: auto;margin-top:10px;" role="alert">
            {{ __('cargo::view.please_configure_package_types') }},
                @if(auth()->user()->can('manage-packages') || $user_role == $admin)
                    <a class="alert-link" href="{{ route('packages.index') }}">{{ __('cargo::view.configure_now') }}</a>
                @else
                    {{ __('cargo::view.configure_now') }}
                @endif
            </div>
        @endif
    </div>
</div>
<!--end::Input group-->

{{-- Inject styles --}}
@section('styles')
    <style>
        label {
            font-weight: bold !important;
        }
        .card-header{
            display: flex !important;
            align-items: center !important;
        }
        .input-group .iti--allow-dropdown,.phone_input {
            width: 100% !important;
        }
    </style>
@endsection

{{-- Inject Scripts --}}
@section('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.min.js" integrity="sha512-foIijUdV0fR0Zew7vmw98E6mOWd9gkGWQBWaoA1EOFAx+pY+N8FmmtIYAVj64R98KeD2wzZh1aHK0JSpKmRH8w==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.js" integrity="sha512-bZAXvpVfp1+9AUHQzekEZaXclsgSlAeEnMJ6LfFAvjqYUVZfcuVXeQoN5LhD7Uw0Jy4NCY9q3kbdEXbwhZUmUQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.min.js" integrity="sha512-0hFHNPMD0WpvGGNbOaTXP0pTO9NkUeVSqW5uFG2f5F9nKyDuHE3T4xnfKhAhnAZWZIO/gBLacwVvxxq0HuZNqw==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.js" integrity="sha512-k59zBVzm+v8h8BmbntzgQeJbRVBK6AL1doDblD1pSZ50rwUwQmC/qMLZ92/8PcbHWpWYeFaf9hCICWXaiMYVRg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js" integrity="sha384-vFJXuSJphROIrBnz7yo7oB41mKfc8JzQZiCq4NCceLEaO4IHwicKwpJf9c9IpFgh" crossorigin="anonymous"></script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js" integrity="sha384-alpBpkh1PFOepccYVYDB4do5UnbKysX5WZXm3XxPqe5iKTfUKjNkCk9SaVuEZflJ" crossorigin="anonymous"></script>
    <script src="{{ asset('assets/global/js/jquery.geocomplete.js') }}"></script>
    <script src="//maps.googleapis.com/maps/api/js?libraries=places&key={{$google_map_key}}"></script>
    <!-- REQUIRED CDN  -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.1/inputmask.js" integrity="sha512-aSxEzzrnqlqgASdjAelu/V291nzZNygMSFMJ0h4PFQ+uwdEz6zKkgsIMbcv0O0ZPwFRNPFWssY7gcL2gZ6/t9A==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.min.css" crossorigin="anonymous" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js" crossorigin="anonymous"></script>
    <script>
        var typeForm = '{{$typeForm}}';
        if(typeForm == 'edit'){
            @if(isset($model))
                @foreach($model->addressess as $key => $address)
                    $('.address-client-{{$address->id}}').each(function(){
                        var address = $(this);
                        var lat = '{{$address->client_lat}}';
                        lat = parseFloat(lat);
                        var lng = '{{$address->client_lng}}';
                        lng = parseFloat(lng);

                        address.geocomplete({
                            map: ".map_canvas_{{$address->id}}.map-client_{{$address->id}}",
                            mapOptions: {
                                zoom: 8,
                                center: { lat: lat, lng: lng },

                            },
                            markerOptions: {
                                draggable: true
                            },
                            details: ".location-client-{{$address->id}}",
                            detailsAttribute: 'data-client',
                            autoselect: true,
                            restoreValueAfterBlur: true,
                        });
                        address.bind("geocode:dragged", function(event, latLng){
                            $("input[data-client=lat]").val(latLng.lat());
                            $("input[data-client=lng]").val(latLng.lng());
                        });
                    });
                @endforeach
            @endif
        }else{
            $('.address-client').each(function(){
                var address = $(this);
                address.geocomplete({
                    map: ".map_canvas.map-client",
                    mapOptions: {
                        zoom: 8,
                        center: { lat: -34.397, lng: 150.644 },
                    },
                    markerOptions: {
                        draggable: true
                    },
                    details: ".location-client",
                    detailsAttribute: 'data-client',
                    autoselect: true,
                    restoreValueAfterBlur: true,
                });
                address.bind("geocode:dragged", function(event, latLng){
                    $("input[data-client=lat]").val(latLng.lat());
                    $("input[data-client=lng]").val(latLng.lng());
                });
            });
        }
        //Address Types Repeater


        function changeStateTo(id) {
            $.get("{{ route('ajax.getAreas') }}?state_id=" + id, function(data) {
                $('select[name ="area_id"]').empty();
                $('select[name ="area_id"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    var old_to_area = {{ old('area_id') ? old('area_id') : 'null' }};
                    if (old_to_area == element['id']) {
                        $('select[name ="area_id"]').append('<option selected value="' + element[
                                'id'] + '">' + JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] +
                            '</option>');
                    } else {
                        $('select[name ="area_id"]').append('<option value="' + element['id'] + '">' +
                            JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] + '</option>');
                    }
                }
            });
        }


        $('#change-state-to').change(function() {
            var id = $(this).val();
            changeStateTo(id);
        });
        @if (old('state_id'))
            changeStateTo({{ old('state_id') }})
        @endif




        $('#kt_repeater_2').repeater({
            initEmpty: false,

            show: function() {
                var repeater_item = $(this);
                @if($googleMap)
                    var map_canvas  = repeater_item.find(".map_canvas.map-client");
                    var map_data    = repeater_item.find(".location-client");
                    repeater_item.find(".address").geocomplete({
                        map: map_canvas,
                        mapOptions: {
                            zoom: 18,
                            center: { lat: -34.397, lng: 150.644 },
                        },
                        markerOptions: {
                            draggable: true
                        },
                        details: map_data,
                        detailsAttribute: "data-client",
                        autoselect: true,
                        restoreValueAfterBlur: true,
                    });
                    repeater_item.find(".address").bind("geocode:dragged", function(event, latLng){
                        repeater_item.find("input[data-client=lat]").val(latLng.lat());
                        repeater_item.find("input[data-client=lng]").val(latLng.lng());
                    });
                @endif


                $(this).slideDown();

                changeCountry();
                changeState();
                selectPlaceholder();
            },

            hide: function(deleteElement) {
                $(this).slideUp(deleteElement);
            },

            isFirstItemUndeletable: true
        });

        function changeCountry()
        {
            $('.change-country-client-address').change(function() {
                var id = $(this).parent().find( ".change-country-client-address" ).val();
                var row = $(this).closest(".row");
                var state_input = row.find(".change-state-client-address");
                var state_name  = state_input.attr("name");

                $.get("{{route('ajax.getStates')}}?country_id=" + id, function(data) {
                    $('select[name ="'+state_name+'"]').empty();

                    $('select[name ="'+state_name+'"]').append('<option value=""></option>');
                    for (let index = 0; index < data.length; index++) {
                        const element = data[index];
                        $('select[name ="'+state_name+'"]').append('<option value="' + element['id'] + '">' + element['name'] + '</option>');
                    }


                });
            });
        }
        changeCountry();

        function changeState()
        {
            $('.change-state-client-address').change(function() {

                var id = $(this).parent().find( ".change-state-client-address" ).val();
                var row = $(this).closest(".row");
                var area_input = row.find(".change-area-client-address");
                var area_name  = area_input.attr("name");
                console.log(area_name);
                $.get("{{route('ajax.getAreas')}}?state_id=" + id, function(data) {
                    $('select[name ="'+area_name+'"]').empty();
                    $('select[name ="'+area_name+'"]').append('<option value=""></option>');
                    for (let index = 0; index < data.length; index++) {
                        const element = data[index];
                        $('select[name ="'+area_name+'"]').append('<option value="' + element['id'] + '">' + JSON.parse(element['name'], true)[`{{app()->getLocale()}}`] + '</option>');
                    }
                });
            });
        }
        changeState();

        function selectPlaceholder()
        {
            $('.select-country').select2({
                placeholder: "{{ __('cargo::view.choose_country') }}",
                width: '100%'
            })
            @if(auth()->user()->can('add-covered-countries') || $user_role == $admin)
                .on('select2:open', () => {
                    $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{route('countries.index')}}?redirect=shipments.create"
                        class="btn btn-primary" >{{ __('cargo::view.manage')}} {{__('cargo::view.covered_countries') }}</a>
                        </li>`);
                });
            @endif

            $('.select-state').select2({
                placeholder: "{{ __('cargo::view.choose_region') }}",
                width: '100%'
            })
            @if(auth()->user()->can('add-covered-regions') || $user_role == $admin)
                .on('select2:open', () => {
                    $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{route('countries.index')}}?redirect=shipments.create"
                        class="btn btn-primary" >{{ __('cargo::view.manage')}} {{__('cargo::view.covered_regions') }}</a>
                        </li>`);
                });
            @endif

            $('.select-area').select2({
                placeholder: "{{ __('cargo::view.choose_area') }}",
                width: '100%'
            })
            @if(auth()->user()->can('manage-areas') || $user_role == $admin)
                .on('select2:open', () => {
                    $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{route('areas.index')}}?redirect=shipments.create"
                        class="btn btn-primary" >{{ __('cargo::view.manage')}} {{__('cargo::view.areas') }}</a>
                        </li>`);
                });
            @endif
        }
        selectPlaceholder();

        $('#check').click(function(){
            const type = $('#password').attr('type') === 'password' ? 'text' : 'password';
            $('#password').prop('type', type);
        });


        $(function () {
            let phoneNumbers_1 = $('.phone_input_1'),
                wrong_number = window.wrong_number_msg,
                required_phone = window.required_phone

                phoneNumbers_1.each(function () {
                let self = $(this),
                    input = self[0],
                    type = self.attr('data-type');
                    // initialise plugin
                let iti = window.intlTelInput(input, {
                    separateDialCode: true,
                    utilsScript: window.static_asset_utils_file,
                    initialCountry: "eg",
                    preferredCountries: ["eg","sa","ae","kw"],
                    autoPlaceholder: "aggressive"
                });
                input.addEventListener("countrychange", function() {
                    $('.phone_input_1').filter(`[data-reflection="${type}"]`).val(iti.getSelectedCountryData().dialCode);
                    $('.country_code_1').val('+'+iti.getSelectedCountryData().dialCode);
                });
                let reset = function() {
                    self.parent().next('.invalid-feedback').remove();
                    self.parent().removeClass('not-valid');
                    self.removeClass("error is-invalid");
                };
                let addError = function(msg) {
                    self.addClass('error is-invalid');
                    self.parent().addClass('not-valid');
                    self.parent().after("<span style='display: block' class=\"invalid-feedback\" role=\"alert\">\n" +
                        " <strong>" + msg + "</strong>\n" +
                        " </span>");
                    return false;
                };
                // on blur: validate
                input.addEventListener('blur', function() {
                    reset();
                    if (self.attr('required')) {
                        if (input.value.trim() == '') {
                            return addError('field is empty')
                        }
                    }
                    if (input.value.trim() && !iti.isValidNumber()) {
                        return addError('reqierd')
                    }
                    // run code if verified
                });
                // on keyup / change flag: reset
                input.addEventListener('change', reset);
                input.addEventListener('keyup', reset);
            })
            $(".number-only").keypress(function(event){
                var ewn = event.which;
                if(ewn >= 48 && ewn <= 57) {
                    return true;
                }
                return false;
            });
            $(".phone-validation").on("submit", function(evt) {
                var phoneField = $(this).find(".phone_input_1");
                if (phoneField.hasClass('error')) {
                    evt.preventDefault();
                    return false
                } else {
                    //do the rest of your validations here
                    $(this).submit();
                }
            });

        });

        $(function () {
            let phoneNumbers_2 = $('.phone_input_2'),
                wrong_number = window.wrong_number_msg,
                required_phone = window.required_phone

                phoneNumbers_2.each(function () {
                let self = $(this),
                    input = self[0],
                    type = self.attr('data-type');
                    // initialise plugin
                let iti = window.intlTelInput(input, {
                    separateDialCode: true,
                    utilsScript: window.static_asset_utils_file,
                    initialCountry: "eg",
                    preferredCountries: ["eg","sa","ae","kw"],
                    autoPlaceholder: "aggressive"
                });
                input.addEventListener("countrychange", function() {
                    $('.phone_input_2').filter(`[data-reflection="${type}"]`).val(iti.getSelectedCountryData().dialCode);
                    $('.country_code_2').val('+'+iti.getSelectedCountryData().dialCode);
                });
                let reset = function() {
                    self.parent().next('.invalid-feedback').remove();
                    self.parent().removeClass('not-valid');
                    self.removeClass("error is-invalid");
                };
                let addError = function(msg) {
                    self.addClass('error is-invalid');
                    self.parent().addClass('not-valid');
                    self.parent().after("<span style='display: block' class=\"invalid-feedback\" role=\"alert\">\n" +
                        " <strong>" + msg + "</strong>\n" +
                        " </span>");
                    return false;
                };
                // on blur: validate
                input.addEventListener('blur', function() {
                    reset();
                    if (self.attr('required')) {
                        if (input.value.trim() == '') {
                            return addError('field is empty')
                        }
                    }
                    if (input.value.trim() && !iti.isValidNumber()) {
                        return addError('reqierd')
                    }
                    // run code if verified
                });
                // on keyup / change flag: reset
                input.addEventListener('change', reset);
                input.addEventListener('keyup', reset);
            })

            $(".number-only").keypress(function(event){
                var ewn = event.which;
                if(ewn >= 48 && ewn <= 57) {
                    return true;
                }
                return false;
            });

            $(".phone-validation").on("submit", function(evt) {
                var phoneField = $(this).find(".phone_input_2");
                if (phoneField.hasClass('error')) {
                    evt.preventDefault();
                    return false
                } else {
                    //do the rest of your validations here
                    $(this).submit();
                }
            });
        });

        // Cost Details Display Functionality
        $(document).ready(function() {
            // Show cost details section on page load for both add and edit modes
            $('#cost-details-section').show();

            // Handle region/state change
            $('#change-state-to').change(function() {
                var stateId = $(this).val();
                var stateName = $(this).find('option:selected').text();

                if (stateId && stateName !== '{{ __("Select State") }}') {
                    $('#selected-region-name').text(stateName);
                    loadStateCosts(stateId);
                    $('#cost-status').removeClass('badge-warning badge-info').addClass('badge-primary').text('{{ __("Loading...") }}');
                } else {
                    $('#selected-region-name').text('{{ __("Please select a region") }}');
                    $('#cost-status').removeClass('badge-success badge-warning badge-primary').addClass('badge-info').text('{{ __("Select Region") }}');
                    showNoCostsMessage();
                }

                // Clear area selection
                $('.select-area').empty().append('<option value="">{{ __("Select Area") }}</option>');
                $('#selected-area-name').text('{{ __("Please select an area") }}');
            });

            // Handle area change
            $('.select-area').change(function() {
                var areaName = $(this).find('option:selected').text();
                if (areaName && areaName !== '{{ __("Select Area") }}') {
                    $('#selected-area-name').text(areaName);
                } else {
                    $('#selected-area-name').text('{{ __("Please select an area") }}');
                }
            });

            // Load state costs via AJAX
            function loadStateCosts(stateId) {
                $.ajax({
                    url: '{{ route("ajax.getStateCosts") }}',
                    type: 'GET',
                    data: { state_id: stateId },
                    success: function(response) {
                        if (response.success) {
                            updateCostDisplay(response.costs);
                        } else {
                            showNoCostsMessage();
                        }
                    },
                    error: function() {
                        showNoCostsMessage();
                    }
                });
            }

            // Update cost display with data
            function updateCostDisplay(costs) {
                $('#def-shipping-cost').text(costs.def_shipping_cost ? '$' + costs.def_shipping_cost : '-');
                $('#def-return-cost').text(costs.def_return_cost ? '$' + costs.def_return_cost : '-');
                $('#def-shipping-cost-gram').text(costs.def_shipping_cost_gram ? '$' + costs.def_shipping_cost_gram : '-');
                $('#def-return-cost-gram').text(costs.def_return_cost_gram ? '$' + costs.def_return_cost_gram : '-');

                $('#cost-status').removeClass('badge-warning').addClass('badge-success').text('{{ __("Costs Available") }}');

                // Generate cost example
                var example = generateCostExample(costs);
                $('#cost-example').html(example);
            }

            // Show no costs message
            function showNoCostsMessage() {
                $('#def-shipping-cost, #def-return-cost, #def-shipping-cost-gram, #def-return-cost-gram').text('-');
                if ($('#selected-region-name').text() === '{{ __("Please select a region") }}') {
                    $('#cost-status').removeClass('badge-success badge-warning badge-primary').addClass('badge-info').text('{{ __("Select Region") }}');
                    $('#cost-example').text('{{ __("Select a region to see cost calculation example") }}');
                } else {
                    $('#cost-status').removeClass('badge-success badge-info badge-primary').addClass('badge-warning').text('{{ __("No Costs Configured") }}');
                    $('#cost-example').text('{{ __("No costs configured for this region. Please configure costs in State Costs Management.") }}');
                }
            }

            // Generate cost calculation example
            function generateCostExample(costs) {
                if (!costs.def_shipping_cost) return '{{ __("No costs configured for calculation example") }}';

                var baseShipping = parseFloat(costs.def_shipping_cost) || 0;
                var extraShipping = parseFloat(costs.def_shipping_cost_gram) || 0;
                var baseReturn = parseFloat(costs.def_return_cost) || 0;
                var extraReturn = parseFloat(costs.def_return_cost_gram) || 0;

                var weight = 5; // Example 5kg package
                var extraWeight = Math.max(0, weight - 3);

                var totalShipping = baseShipping + (extraWeight * extraShipping);
                var totalReturn = baseReturn + (extraWeight * extraReturn);

                return '<strong>{{ __("Example") }} (' + weight + 'kg {{ __("package") }}):</strong><br>' +
                       '{{ __("Shipping") }}: $' + baseShipping + ' ({{ __("base") }}) + $' + (extraWeight * extraShipping).toFixed(2) + ' (' + extraWeight + 'kg × $' + extraShipping + ') = <strong>$' + totalShipping.toFixed(2) + '</strong><br>' +
                       '{{ __("Return") }}: $' + baseReturn + ' ({{ __("base") }}) + $' + (extraWeight * extraReturn).toFixed(2) + ' (' + extraWeight + 'kg × $' + extraReturn + ') = <strong>$' + totalReturn.toFixed(2) + '</strong>';
            }

            // Initialize on page load if editing
            @if($typeForm == 'edit' && isset($model) && $model->state_id)
                var initialStateId = '{{ $model->state_id }}';
                var initialStateName = $('#change-state-to option:selected').text();
                if (initialStateId) {
                    $('#selected-region-name').text(initialStateName);
                    loadStateCosts(initialStateId);
                    $('#cost-details-section').show();
                }

                @if(isset($model) && $model->area_id)
                    var initialAreaName = $('.select-area option:selected').text();
                    if (initialAreaName) {
                        $('#selected-area-name').text(initialAreaName);
                    }
                @endif
            @endif

            // Client State Cost Override Functionality
            console.log('Initializing client state cost override...');
            initializeClientStateCostOverride();
        });

        // Client State Cost Override Functions
        function initializeClientStateCostOverride() {
            // Show the override section
            $('#client-state-cost-override-section').show();

            // States loaded directly from database - same query as region dropdown
            var stateCount = $('#cost-override-state option').length - 1;
            console.log('States loaded for cost override:', stateCount, 'states available');

            // Handle state selection for override
            $('#cost-override-state').change(function() {
                var stateId = $(this).val();
                var stateName = $(this).find('option:selected').text();

                console.log('State dropdown changed:', stateId, stateName);

                if (stateId) {
                    loadStateOverrideCosts(stateId, stateName);
                } else {
                    hideOverrideFields();
                }
            });

            // Handle save override
            $('#save-override-btn').click(function() {
                console.log('Save override button clicked');
                saveClientStateCostOverride();
            });

            // Handle remove override
            $('#remove-override-btn').click(function() {
                removeClientStateCostOverride();
            });

            // Handle cost field changes for real-time comparison
            $('#cost-override-fields input').on('input', function() {
                updateCostComparison();
            });


        }



        function loadStateOverrideCosts(stateId, stateName) {
            var clientId = '{{ isset($model) ? $model->id : 0 }}';

            console.log('Loading state costs for:', {
                clientId: clientId,
                stateId: stateId,
                stateName: stateName
            });

            $.ajax({
                url: '{{ route("ajax.getClientStateCosts") }}',
                type: 'GET',
                data: {
                    client_id: clientId,
                    state_id: stateId
                },
                success: function(response) {
                    console.log('Load response:', response);
                    if (response.success) {
                        showOverrideFields(response.costs, response.default_costs, stateName);
                    } else {
                        showOverrideFields(null, response.default_costs, stateName);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Load error:', xhr.responseText);
                    $('#override-status').removeClass().addClass('badge badge-danger').text('{{ __("Error Loading Costs") }}');
                }
            });
        }

        function showOverrideFields(clientCosts, defaultCosts, stateName) {
            $('#cost-override-fields').show();
            $('#override-comparison').show();

            // Populate fields with client costs or defaults
            if (clientCosts) {
                $('#override-def-shipping-cost').val(clientCosts.def_shipping_cost || '');
                $('#override-def-return-cost').val(clientCosts.def_return_cost || '');
                $('#override-def-shipping-cost-gram').val(clientCosts.def_shipping_cost_gram || '');
                $('#override-def-return-cost-gram').val(clientCosts.def_return_cost_gram || '');

                $('#override-status').removeClass().addClass('badge badge-warning').text('{{ __("Custom Costs Active") }}');
                $('#save-override-btn').show().text('{{ __("Update Override") }}');
                $('#remove-override-btn').show();
            } else {
                $('#override-def-shipping-cost').val(defaultCosts.def_shipping_cost || '');
                $('#override-def-return-cost').val(defaultCosts.def_return_cost || '');
                $('#override-def-shipping-cost-gram').val(defaultCosts.def_shipping_cost_gram || '');
                $('#override-def-return-cost-gram').val(defaultCosts.def_return_cost_gram || '');

                $('#override-status').removeClass().addClass('badge badge-info').text('{{ __("Using Default Costs") }}');
                $('#save-override-btn').show().text('{{ __("Save Override") }}');
                $('#remove-override-btn').hide();
            }

            // Show default costs
            $('#default-costs-display').html(
                '{{ __("Shipping") }}: $' + (defaultCosts.def_shipping_cost || '0') + ' + $' + (defaultCosts.def_shipping_cost_gram || '0') + '/kg<br>' +
                '{{ __("Return") }}: $' + (defaultCosts.def_return_cost || '0') + ' + $' + (defaultCosts.def_return_cost_gram || '0') + '/kg'
            );

            updateCostComparison();
        }

        function hideOverrideFields() {
            $('#cost-override-fields').hide();
            $('#override-comparison').hide();
            $('#save-override-btn').hide();
            $('#remove-override-btn').hide();
            $('#override-status').removeClass().addClass('badge badge-info').text('{{ __("Select State") }}');
        }

        function updateCostComparison() {
            var shipping = $('#override-def-shipping-cost').val() || '0';
            var shippingGram = $('#override-def-shipping-cost-gram').val() || '0';
            var returnCost = $('#override-def-return-cost').val() || '0';
            var returnGram = $('#override-def-return-cost-gram').val() || '0';

            $('#custom-costs-display').html(
                '{{ __("Shipping") }}: $' + shipping + ' + $' + shippingGram + '/kg<br>' +
                '{{ __("Return") }}: $' + returnCost + ' + $' + returnGram + '/kg'
            );
        }

        function saveClientStateCostOverride() {
            var clientId = '{{ isset($model) ? $model->id : 0 }}';
            var stateId = $('#cost-override-state').val();

            console.log('Saving client state costs:', {
                clientId: clientId,
                stateId: stateId
            });

            if (!clientId || clientId == '0') {
                alert('{{ __("Please save the client first before setting custom costs") }}');
                return;
            }

            if (!stateId) {
                alert('{{ __("Please select a state") }}');
                return;
            }

            var costs = {
                def_shipping_cost: $('#override-def-shipping-cost').val() || 0,
                def_return_cost: $('#override-def-return-cost').val() || 0,
                def_shipping_cost_gram: $('#override-def-shipping-cost-gram').val() || 0,
                def_return_cost_gram: $('#override-def-return-cost-gram').val() || 0
            };

            console.log('Costs to save:', costs);

            $.ajax({
                url: '{{ route("ajax.saveClientStateCosts") }}',
                type: 'POST',
                data: {
                    client_id: clientId,
                    state_id: stateId,
                    costs: costs,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    console.log('Save response:', response);
                    if (response.success) {
                        $('#override-status').removeClass().addClass('badge badge-success').text('{{ __("Saved Successfully") }}');
                        $('#save-override-btn').text('{{ __("Update Override") }}');
                        $('#remove-override-btn').show();

                        // Reload the costs to show the saved values
                        var stateName = $('#cost-override-state option:selected').text();
                        loadStateOverrideCosts(stateId, stateName);

                        setTimeout(function() {
                            $('#override-status').removeClass().addClass('badge badge-warning').text('{{ __("Custom Costs Active") }}');
                        }, 2000);
                    } else {
                        alert('{{ __("Error saving costs") }}: ' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.log('Save error:', xhr.responseText);
                    alert('{{ __("Error saving costs") }}: ' + error);
                }
            });
        }

        function removeClientStateCostOverride() {
            var clientId = '{{ isset($model) ? $model->id : 0 }}';
            var stateId = $('#cost-override-state').val();

            if (!confirm('{{ __("Are you sure you want to remove the custom costs for this state? This will revert to default state costs.") }}')) {
                return;
            }

            $.ajax({
                url: '{{ route("ajax.removeClientStateCosts") }}',
                type: 'DELETE',
                data: {
                    client_id: clientId,
                    state_id: stateId,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        // Reload the state to show default costs
                        var stateName = $('#cost-override-state option:selected').text();
                        loadStateOverrideCosts(stateId, stateName);
                    } else {
                        alert('{{ __("Error removing costs") }}: ' + response.message);
                    }
                },
                error: function() {
                    alert('{{ __("Error removing costs") }}');
                }
            });
        }


    </script>
@endsection
