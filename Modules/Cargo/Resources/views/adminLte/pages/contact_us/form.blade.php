@csrf

@php
    $user_role = auth()->user()->role;
    $admin = 1;

    $is_def_mile_or_fees = Modules\Cargo\Entities\ShipmentSetting::getVal('is_def_mile_or_fees');

    $googleSettings = resolve(\app\Models\GoogleSettings::class)->toArray();
    $googleMap = json_decode($googleSettings['google_map'], true);
    $google_map_key = '';
    if ($googleMap) {
        $google_map_key = $googleMap['google_map_key'];
    }

    $countries = Modules\Cargo\Entities\Country::where('covered', 1)->get();
@endphp



<!--end::Col-->
<div @if (!isset($model)) hidden @endif class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">{{ __('User') }}</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <input type="text" readonly disabled
                class="form-control form-control-lg @error('name') is-invalid @enderror" placeholder="{{ __('name') }}"
                value="{{ old('name', isset($model) ? $model->userNameClear() : '') }}" />
            @error('name')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>



<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">{{ __('Title') }}</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <input type="text"
                @if (isset($model)) readonly disabled
                @else
                required @endif
                name="title" class="form-control form-control-lg @error('title') is-invalid @enderror"
                placeholder="{{ __('title') }}"
                @if (!empty(request('type')) && request('type') == 'pickup_request') value="Pickup Request"  readonly    
                @else
                    value="{{ old('title', isset($model) ? $model->title : '') }}" @endif />
            @error('title')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>



<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 required">{{ __('Message') }}</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <textarea @if (isset($model)) readonly 
            @else
            required="required" 
            @endif
                class="w-100 " @error('message') is-invalid @enderror placeholder="@lang('blog::front.field_comment')" id="comment_content" name="message" rows="8" maxlength="65000">  {{ old('name', isset($model) ? $model->message : '') }}   </textarea>

            @error('message')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>






<!--begin::Input group -- Branch -->
<div @if (!isset($model)) hidden @endif class="row mb-6">

    <!--begin::Input group-->
    <div class="fv-row col-lg-12 form-group">
        <!--begin::Label-->
        <label class="col-form-label fw-bold fs-6 required">{{ __('status') }}</label>
        <!--end::Label-->
        <select required class="form-control  @error('status') is-invalid @enderror" name="status"
            data-control="select2" required data-placeholder="{{ __('status') }}" data-allow-clear="true">
            <option value="pending" {{ old('status') == 'pending' ? 'selected' : '' }}
                @if ($typeForm == 'edit') {{ $model->status == 'pending' ? 'selected' : '' }} @endif>Pending
            </option>

            <option value="done" {{ old('status') == 'done' ? 'selected' : '' }}
                @if ($typeForm == 'edit') {{ $model->status == 'done' ? 'selected' : '' }} @endif>done</option>

            <option value="cancel" {{ old('status') == 'cancel' ? 'selected' : '' }}
                @if ($typeForm == 'edit') {{ $model->status == 'cancel' ? 'selected' : '' }} @endif>cancel
            </option>


        </select>
        @error('status')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <!--end::Input group-->
</div>
<!--end::Input group-->










<!--end::Input group-->





{{-- Inject styles --}}
@section('styles')
    <style>
        label {
            font-weight: bold !important;
        }

        .card-header {
            display: flex !important;
            align-items: center !important;
        }

        .input-group .iti--allow-dropdown,
        .phone_input {
            width: 100% !important;
        }
    </style>
@endsection

{{-- Inject Scripts --}}
@section('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.min.js"
        integrity="sha512-foIijUdV0fR0Zew7vmw98E6mOWd9gkGWQBWaoA1EOFAx+pY+N8FmmtIYAVj64R98KeD2wzZh1aHK0JSpKmRH8w=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.js"
        integrity="sha512-bZAXvpVfp1+9AUHQzekEZaXclsgSlAeEnMJ6LfFAvjqYUVZfcuVXeQoN5LhD7Uw0Jy4NCY9q3kbdEXbwhZUmUQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.min.js"
        integrity="sha512-0hFHNPMD0WpvGGNbOaTXP0pTO9NkUeVSqW5uFG2f5F9nKyDuHE3T4xnfKhAhnAZWZIO/gBLacwVvxxq0HuZNqw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.js"
        integrity="sha512-k59zBVzm+v8h8BmbntzgQeJbRVBK6AL1doDblD1pSZ50rwUwQmC/qMLZ92/8PcbHWpWYeFaf9hCICWXaiMYVRg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"
        integrity="sha384-vFJXuSJphROIrBnz7yo7oB41mKfc8JzQZiCq4NCceLEaO4IHwicKwpJf9c9IpFgh" crossorigin="anonymous">
    </script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js"
        integrity="sha384-alpBpkh1PFOepccYVYDB4do5UnbKysX5WZXm3XxPqe5iKTfUKjNkCk9SaVuEZflJ" crossorigin="anonymous">
    </script>
    <script src="{{ asset('assets/global/js/jquery.geocomplete.js') }}"></script>
    <script src="//maps.googleapis.com/maps/api/js?libraries=places&key={{ $google_map_key }}"></script>
    <!-- REQUIRED CDN  -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.1/inputmask.js"
        integrity="sha512-aSxEzzrnqlqgASdjAelu/V291nzZNygMSFMJ0h4PFQ+uwdEz6zKkgsIMbcv0O0ZPwFRNPFWssY7gcL2gZ6/t9A=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"
        crossorigin="anonymous"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.min.css"
        crossorigin="anonymous" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js" crossorigin="anonymous"></script>

    <script></script>
@endsection
