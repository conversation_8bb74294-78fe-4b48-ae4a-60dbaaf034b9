@csrf


@php
    $user_role = auth()->user()->role;
    $admin = 1;
    $auth_staff = 0;
    $auth_branch = 3;
    $auth_client = 4;

    $userBranch = Modules\Cargo\Entities\Branch::where('user_id', auth()->user()->id)->first();
    $userStaff = Modules\Cargo\Entities\Staff::where('user_id', auth()->user()->id)->first();
    $userClient = Modules\Cargo\Entities\Client::where('user_id', auth()->user()->id)->first();
    $userReciver = Modules\Cargo\Entities\Receiver::where('user_id', auth()->user()->id)->first();

    $branches = Modules\Cargo\Entities\Branch::where('is_archived', 0)->get();
    $clients = Modules\Cargo\Entities\Client::where('is_archived', 0)->get();
    $Recivers = Modules\Cargo\Entities\Receiver::where('is_archived', 0)->get();
    if ($user_role == $auth_branch) {
        $branches = Modules\Cargo\Entities\Branch::where('id', $userBranch->id)->get();
        $clients = Modules\Cargo\Entities\Client::where('branch_id', $userBranch->id)->get();
        $Recivers = Modules\Cargo\Entities\Receiver::where('branch_id', $userBranch->id)->get();
    } elseif (auth()->user()->can('create-shipments') && $user_role == $auth_staff) {
        $branches = Modules\Cargo\Entities\Branch::where('id', $userStaff->branch_id)->get();
        $clients = Modules\Cargo\Entities\Client::where('branch_id', $userStaff->branch_id)->get();
        $Recivers = Modules\Cargo\Entities\Receiver::where('branch_id', $userStaff->branch_id)->get();
    }

    $countries = Modules\Cargo\Entities\Country::where('covered', 1)->get();
    $packages = Modules\Cargo\Entities\Package::all();
    $deliveryTimes = Modules\Cargo\Entities\DeliveryTime::all();

    // is_def_mile_or_fees if result 1 for mile if result 2 for fees
    $is_def_mile_or_fees = Modules\Cargo\Entities\ShipmentSetting::getVal('is_def_mile_or_fees');
    if (!$is_def_mile_or_fees) {
        $is_def_mile_or_fees = 0;
    }

    $googleSettings = resolve(\app\Models\GoogleSettings::class)->toArray();
    $googleMap = json_decode($googleSettings['google_map'], true);
    $google_map_key = '';
    if ($googleMap) {
        $google_map_key = $googleMap['google_map_key'];
    }

    $paymentSettings = resolve(\Modules\Payments\Entities\PaymentSetting::class)->toArray();
@endphp

<div class="row">
    <div class="col-lg-12">

        <div class="form-group row">
            <div class="col-9 col-form-label">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.shipment_type') }}</label>


                <div class="form-group">
                    <select class="form-control select-  @error('Shipment.type') is-invalid @enderror"
                        data-control="select2" data-placeholder="{{ __('cargo::view.table.type') }}"
                        data-allow-clear="true" required name="Shipment[type]">
                        <option value="1" @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_shipment_type') == '1') selected @endif
                            {{ old('Shipment.type') == 1 ? 'checked' : '' }}
                            @if ($typeForm == 'edit') {{ $model->type == 1 ? 'checked' : '' }} @endif>
                            {{ __('cargo::view.Pickup_For_door_to_door_delivery') }}
                        </option>

                        {{--
                        <option @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_shipment_type') == '2') selected @endif
                            {{ old('Shipment.type') == 2 ? 'checked' : '' }}
                            @if ($typeForm == 'edit') {{ $model->type == 2 ? 'checked' : '' }} @endif>
                            {{ __('DropOff') }}
                        </option> --}}

                        <option value="3" @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_shipment_type') == '3') selected @endif
                            {{ old('Shipment.type') == 3 ? 'checked' : '' }}
                            @if ($typeForm == 'edit') {{ $model->type == 3 ? 'checked' : '' }} @endif>
                            {{ __('RETURN FROM RECIEVER') }}
                        </option>

                        {{-- <option @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_shipment_type') == '4') selected @endif
                            {{ old('Shipment.type') == 4 ? 'checked' : '' }}
                            @if ($typeForm == 'edit') {{ $model->type == 4 ? 'checked' : '' }} @endif>
                            {{ __('EXCHANGE') }}
                        </option> --}}


                    </select>
                    @error('Shipment.type')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>



            </div>
        </div>

        @php
    $user = auth()->user();

    if ($user_role == $auth_client) {
        $client = Modules\Cargo\Entities\Client::where('user_id', $user->id)->first();
    } else {
        $client = null;
    }

@endphp

        <div class="form-group row">
            <div class="col-9 col-form-label">
                <label class="col-form-label fw-bold fs-6 required">{{ __('Can Open This Shipment ?') }}</label>


                <div class="form-group">
                    <select class="form-control select-  @error('Shipment.open_shipment') is-invalid @enderror"
                        data-control="select2" data-placeholder="{{ __('open_shipment') }}"
                        data-allow-clear="true" required name="Shipment[open_shipment]">
                        <option value="no"
                            {{ old('Shipment.open_shipment') == 'no' ? 'checked' : '' }}
                            @if ($typeForm == 'edit') {{ $model->open_shipment == 'no' ? 'checked' : '' }} @endif>
                            {{ __('no') }}
                        </option>

                        <option value="yes"
                        {{ old('Shipment.open_shipment') == 'yes' ? 'checked' : '' }}
                        @if ($typeForm == 'edit') {{ $model->open_shipment == 'yes' ? 'checked' : '' }} @endif>
                        {{ __('yes') }}
                        </option>



                    </select>
                    @error('Shipment.open_shipment')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>



            </div>
        </div>

        <hr>
        <div class="row">
            @if (Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == '1' ||
                    Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == null)
                <div class="col-md-12">
                @else
                    <div class="col-md-12">
            @endif
            <div hidden class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.table.branch') }}</label>
                <select class="form-control select-branch  @error('Shipment.branch_id') is-invalid @enderror"
                    data-control="select2" data-placeholder="{{ __('cargo::view.table.choose_branch') }}"
                    data-allow-clear="true" name="Shipment[branch_id]">
                    <option></option>
                    @foreach ($branches as $branch)
                        @if ($user_role == $auth_branch)
                            <option selected @if ($userBranch->id == $branch->id) selected @endif
                                value="{{ $branch->id }}"
                                {{ old('Shipment.branch_id') == $branch->id ? 'selected' : '' }}
                                @if ($typeForm == 'edit') {{ $model->branch_id == $branch->id ? 'selected' : '' }} @endif>
                                {{ $branch->name }}
                            </option>
                        @else
                            <option selected @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_branch') == $branch->id) selected @endif
                                value="{{ $branch->id }}"
                                {{ old('Shipment.branch_id') == $branch->id ? 'selected' : '' }}
                                @if ($typeForm == 'edit') {{ $model->branch_id == $branch->id ? 'selected' : '' }} @endif>
                                {{ $branch->name }}

                            </option>
                        @endif
                    @endforeach

                </select>
                @error('Shipment.branch_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

        @if (Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == '1' ||
                Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == null)
            <div class="col-md-4">
                <div class="form-group">
                    <label class="col-form-label fw-bold fs-6">{{ __('Pickup Date') }}</label>
                    <div class="input-group date">
                        @php
                            $defult_shipping_date = Modules\Cargo\Entities\ShipmentSetting::getVal('def_shipping_date');
                            if ($defult_shipping_date == null) {
                                $shipping_data = \Carbon\Carbon::now()->addDays(0);
                            } else {
                                $shipping_data = \Carbon\Carbon::now()->addDays($defult_shipping_date);
                            }

                        @endphp
                        <input type="text" placeholder="{{ __('Pickup Date') }}"
                            value="{{ old('Shipment.shipping_date', isset($model) ? $model->shipping_date : $shipping_data->toDateString()) }}"
                            name="Shipment[shipping_date]" autocomplete="off" class="form-control"
                            id="kt_datepicker_3" />
                        <div class="input-group-append">
                            <span class="input-group-text" style="padding: 10px !important;">
                                <i class="la la-calendar"></i>
                            </span>
                        </div>
                    </div>

                </div>
            </div>
        @endif

        @if (Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == '1' ||
                Modules\Cargo\Entities\ShipmentSetting::getVal('is_date_required') == null)
            <div class="col-md-4">
            @else
                <div class="col-md-6">
        @endif
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.collection_time') }}</label>
            <div class="input-group date">
                <input type="text" placeholder="{{ __('cargo::view.collection_time') }}"
                    value="{{ old('Shipment.collection_time', isset($model) ? $model->collection_time : '') }}"
                    name="Shipment[collection_time]" autocomplete="off" class="form-control" id="kt_timepicker_3" />
                <div class="input-group-append">
                    <span class="input-group-text" style="padding: 10px !important;">
                        <i class="la la-clock-o"></i>
                    </span>
                </div>
            </div>

        </div>
    </div>

</div>
<div class="row">

    <div class="col-md-6">
        <div class="form-group client-select">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.client_sender') }}</label>
            @if ($user_role == $auth_client)
                <input type="text" placeholder="" class="form-control" name=""
                    value="{{ $userClient->name }}" disabled>
                <input type="hidden" name="Shipment[client_id]" value="{{ $userClient->id }}">
            @else
                <select id="client-id" onchange="selectIdTriggered()"
                    class="form-control select-client  @error('Shipment.client_id') is-invalid @enderror"
                    data-control="select2" data-placeholder="{{ __('cargo::view.choose_client') }}"
                    data-allow-clear="true" name="Shipment[client_id]">
                    <option></option>
                    @foreach ($clients as $client)
                        <option value="{{ $client->id }}" data-phone="{{ $client->responsible_mobile }}"
                            data-country="{{ $client->country_code }}"
                            {{ old('Shipment.client_id') == $client->id ? 'selected' : '' }}
                            @if ($typeForm == 'edit') {{ $model->client_id == $client->id ? 'selected' : '' }} @endif>
                            {{ $client->name }}</option>
                    @endforeach

                </select>
                @error('Shipment.client_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            @endif

        </div>
    </div>

    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required"
                style=" display: block; ">{{ __('cargo::view.client_phone') }}</label>

            @if ($user_role == $auth_client)
                <input type="tel" disabled name="Shipment[client_phone]"
                    placeholder="{{ __('cargo::view.client_phone') }}" id="client_phone"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->client_phone : ($userClient->responsible_mobile ?? '') }}"
                    class="phone_input phone_input_1 form-control @error('Shipment.client_phone') is-invalid @enderror" />
                <input type="hidden" class="country_code country_code_1" name="Shipment[country_code]"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->country_code : ($userClient->country_code ?? base_country_code()) }}"
                    data-reflection="phone">
            @else
                <input type="tel" name="Shipment[client_phone]"
                    placeholder="{{ __('cargo::view.client_phone') }}" id="client_phone"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->client_phone : '' }}"
                    class="phone_input phone_input_1 form-control @error('Shipment.client_phone') is-invalid @enderror" />
                <input type="hidden" class="country_code country_code_1" id="country_code"
                    name="Shipment[country_code]"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->country_code : base_country_code() }}"
                    data-reflection="phone">
            @endif

            @error('Shipment.client_phone')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror

        </div>
    </div>
    <div class="col-md-12">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.client_address') }}</label>
            <select id="client-addresses" name="Shipment[client_address]"
                class="form-control select-address @error('Shipment.client_id') is-invalid @enderror">
                {{-- <option value=""></option> --}}
                @if ($typeForm == 'edit')
                    @foreach (Modules\Cargo\Entities\ClientAddress::where('client_id', $model->client_id)->where('is_archived', 0)->get() as $address)
                        <option value="{{ $address->id }}"
                            {{ old('Shipment.client_address') == $address->id ? 'selected' : '' }}
                            @if ($typeForm == 'edit') {{ $model->client_address == $address->id ? 'selected' : '' }} @endif>
                            {{ $address->address }}</option>
                    @endforeach
                @endif

            </select>
            @error('Shipment.client_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>

    <div class="p-3 mb-4 mt-4 col-md-12" id="show_address_div" style="border: 1px solid #e4e6ef; display:none">
        <div class="row">
            @csrf
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.country') }}</label>
                    <select id="change-country-client-address" name="country_id"
                        class="form-control select-country @error('country_id') is-invalid @enderror">
                        <option value=""></option>
                        @foreach ($countries as $country)
                            <option value="{{ $country->id }}">{{ $country->name }}</option>
                        @endforeach
                    </select>
                    @error('country_id')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.region') }}</label>
                    <select @error('state_id') is-invalid @enderror id="change-state-client-address" name="state_id"
                        class="form-control select-state">
                        <option value=""></option>

                    </select>
                    @error('state_id')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.area') }}</label>
            <select @error('area_id') is-invalid @enderror name="area_id" style="display: block !important;"
                class="form-control select-area">
                <option value=""></option>

            </select>
            @error('area_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.address') }}</label>
            <input @error('client_address') is-invalid @enderror type="text"
                placeholder="{{ __('cargo::view.address') }}" name="client_address" class="form-control" />
            @error('client_address')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
        @if ($googleMap)
            <div class="location-client">
                <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.location') }}</label>
                <input type="text" class="form-control address-client "
                    placeholder="{{ __('cargo::view.location') }}" name="client_street_address_map" rel="client"
                    value="" />
                <input type="hidden" class="form-control lat" data-client="lat" name="client_lat" />
                <input type="hidden" class="form-control lng" data-client="lng" name="client_lng" />
                <input type="hidden" class="form-control url" data-client="url" name="client_url" />

                <div class="mt-2 col-sm-12 map_canvas map-client" style="width:100%;height:300px;"></div>
                <span class="form-text text-muted">{{ 'Change the pin to select the right location' }}</span>
            </div>
        @endif
        <div class="mt-4">
            <button type="button" class="btn btn-primary"
                onclick="AddNewClientAddress()">{{ __('cargo::view.save') }}</button>
            <button type="button" class="btn btn-secondary"
                onclick="closeAddressDiv()">{{ __('cargo::view.close') }}</button>
        </div>
    </div>


    <div class="col-md-6">
        <div class="form-group client-select">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.receiver_name') }}</label>
            @if ($user_role == $auth_client)
                <input type="text" placeholder="" value="  @if ($typeForm == 'edit') {{ $model->reciver_name  ?  $model->reciver_name : '' }} @endif " class="form-control" name="Shipment[reciver_name]"
                    value="">
            @else
                <select id="reciver-id"
                    class="form-control select-reciver  @error('Shipment.reciver_name') is-invalid @enderror"
                    data-control="select2" data-placeholder="{{ __('cargo::view.choose_client') }}"
                    data-allow-clear="true" name="Shipment[reciver_name]">
                    <option></option>
                    @foreach ($Recivers as $client)
                        <option value="{{ $client->name }}" data-phone="{{ $client->receiver_mobile }}"
                            data-country="{{ $client->country_code }}" data-address="{{ $client->reciver_address }}"
                            {{ old('Shipment.reciver_name') == $client->name ? 'selected' : '' }}
                            @if ($typeForm == 'edit') {{ $model->reciver_name == $client->name ? 'selected' : '' }} @endif>
                            {{ $client->name }}</option>
                    @endforeach
                </select>
                @error('Shipment.reciver_name')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            @endif

        </div>
    </div>

    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required"
                style=" display: block; ">{{ __('cargo::view.receiver_phone') }}</label>

            @if ($user_role == $auth_client)
                <input type="tel" name="Shipment[reciver_phone]"
                    placeholder="{{ __('cargo::view.reciver_phone') }}" id="reciver_phone"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->reciver_phone : '' }}"
                    class="phone_input phone_input_2 form-control @error('Shipment.reciver_phone') is-invalid @enderror" />
                <input type="hidden" class="country_code country_code_2" name="Shipment[reciver_country_code]"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->reciver_country_code : base_country_code() }}"
                    data-reflection="phone">
            @else
                <input type="tel" name="Shipment[reciver_phone]"
                    placeholder="{{ __('cargo::view.reciver_phone') }}" id="reciver_phone"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->reciver_phone : '' }}"
                    class="phone_input phone_input_2 form-control @error('Shipment.reciver_phone') is-invalid @enderror" />
                <input type="hidden" class="country_code country_code_2" id="reciver_country_code"
                    name="Shipment[reciver_country_code]"
                    value="{{ $typeForm == 'edit' && isset($model) ? $model->reciver_country_code : base_country_code() }}"
                    data-reflection="phone">
            @endif

            @error('Shipment.reciver_phone')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror

        </div>
    </div>

    <div class="col-md-12">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.receiver_address') }}</label>
            <input type="text" id="reciver_address"
                value="{{ old('Shipment.reciver_address', isset($model) ? $model->reciver_address : '') }}"
                placeholder="{{ __('cargo::view.receiver_address') }}" name="Shipment[reciver_address]"
                class="form-control @error('Shipment.reciver_address') is-invalid @enderror  address"
                data-reflection="address" />
            @error('Shipment.reciver_address')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>




    @if ($googleMap)
        <div class="col-md-12">
            <div class="location-receiver">
                <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.receiver_location') }}</label>
                <input type="text" class="form-control address-receiver "
                    placeholder="{{ __('cargo::view.receiver_location') }}"
                    name="Shipment[reciver_street_address_map]"
                    value="{{ old('Shipment.reciver_street_address_map', isset($model) ? $model->reciver_street_address_map : '') }}"
                    rel="receiver" value="" />
                <input type="hidden" class="form-control lat" data-receiver="lat" name="Shipment[reciver_lat]"
                    value="{{ old('Shipment.reciver_lat', isset($model) ? $model->reciver_lat : '') }}" />
                <input type="hidden" class="form-control lng" data-receiver="lng" name="Shipment[reciver_lng]"
                    value="{{ old('Shipment.reciver_lng', isset($model) ? $model->reciver_lng : '') }}" />
                <input type="hidden" class="form-control url" data-receiver="url" name="Shipment[reciver_url]"
                    value="{{ old('Shipment.reciver_url', isset($model) ? $model->reciver_url : '') }}" />

                <div class="mt-2 col-sm-12 map_canvas map-receiver" style="width:100%;height:300px;"></div>
                <span class="form-text text-muted">{{ 'Change the pin to select the right location' }}</span>
            </div>
        </div>
    @endif

</div>
<hr>
<div class="row mb-5">
    <div hidden class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.from_country') }}</label>
            <select id="change-country" name="Shipment[from_country_id]"
                class="form-control select-country @error('Shipment.from_country_id') is-invalid @enderror">
                <option value=""></option>
                @foreach ($countries as $country)
                    <option value="{{ $country->id }}"
                        {{ old('Shipment.from_country_id') == $country->id ? 'selected' : '' }}
                        @if ($typeForm == 'edit') {{ $model->from_country_id == $country->id ? 'selected' : '' }} @endif
                        {{ $country->id == 65 ? 'selected' : '' }}>{{ $country->name }}</option>
                @endforeach
            </select>
            @error('Shipment.from_country_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <div hidden class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.to_country') }}</label>
            <select id="change-country-to" name="Shipment[to_country_id]"
                class="form-control select-country @error('Shipment.to_country_id') is-invalid @enderror">
                <option value=""></option>
                @foreach ($countries as $country)
                    <option value="{{ $country->id }}"
                        {{ old('Shipment.to_country_id') == $country->id ? 'selected' : '' }}
                        @if ($typeForm == 'edit') {{ $model->to_country_id == $country->id ? 'selected' : '' }} @endif
                        {{ $country->id == 65 ? 'selected' : '' }}>{{ $country->name }}</option>
                @endforeach
            </select>
            @error('Shipment.to_country_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
</div>







<div class="row mb-5">


    @if (!empty($client))

        <div hidden class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.from_region') }}</label>
                <select name="Shipment[from_state_id]"
                    class="form-control select-state @error('Shipment.from_state_id') is-invalid @enderror">
                    <option selected value="{{ $client->state_id ?? 0 }}"></option>

                </select>
                @error('Shipment.from_state_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
    @else
        <div class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.from_region') }}</label>
                <select id="change-state-from" name="Shipment[from_state_id]"
                    class="form-control select-state @error('Shipment.from_state_id') is-invalid @enderror">
                    <option value=""></option>
                    @if ($typeForm == 'edit')
                        @foreach (Modules\Cargo\Entities\State::where('country_id', $model->from_country_id)->where('covered', 1)->get() as $item)
                            <option value="{{ $item->id }}"
                                @if ($typeForm == 'edit') {{ $model->from_state_id == $item->id ? 'selected' : '' }} @endif>
                                {{ $item->name }}</option>
                        @endforeach
                    @endif
                </select>
                @error('Shipment.from_state_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>

    @endif


    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.to_region') }}</label>
            <select id="change-state-to" name="Shipment[to_state_id]"
                class="form-control select-state @error('Shipment.to_state_id') is-invalid @enderror">
                <option value=""></option>
                @if ($typeForm == 'edit')
                    @foreach (Modules\Cargo\Entities\State::where('country_id', $model->to_country_id)->where('covered', 1)->get() as $item)
                        <option value="{{ $item->id }}"
                            @if ($typeForm == 'edit') {{ $model->to_state_id == $item->id ? 'selected' : '' }} @endif>
                            {{ $item->name }}</option>
                    @endforeach
                @endif
            </select>
            @error('Shipment.to_state_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
</div>
<div class="row mb-5">


    @if (!empty($client))

        <div hidden class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.from_area') }}</label>
                <select name="Shipment[from_area_id]" id="from_area_id"
                    class="form-control select-area @error('Shipment.from_area_id') is-invalid @enderror">
                    <option selected value="{{ $client->area_id ?? 0 }}"></option>

                </select>
                @error('Shipment.from_area_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
    @else
        <div class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.from_area') }}</label>
                <select name="Shipment[from_area_id]" id="from_area_id"
                    class="form-control select-area @error('Shipment.from_area_id') is-invalid @enderror">
                    <option value=""></option>
                    @if ($typeForm == 'edit')
                        @foreach (Modules\Cargo\Entities\Area::where('state_id', $model->from_state_id)->get() as $item)
                            <option value="{{ $item->id }}"
                                @if ($typeForm == 'edit') {{ $model->from_area_id == $item->id ? 'selected' : '' }} @endif>
                                {{ json_decode($item->name, true)[app()->getLocale()] }}</option>
                        @endforeach
                    @endif
                </select>
                @error('Shipment.from_area_id')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
    @endif


    <div class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.to_area') }}</label>
            <select name="Shipment[to_area_id]"
                class="form-control select-area @error('Shipment.to_area_id') is-invalid @enderror">
                <option value=""></option>
                @if ($typeForm == 'edit')
                    @foreach (Modules\Cargo\Entities\Area::where('state_id', $model->to_state_id)->get() as $item)
                        <option value="{{ $item->id }}"
                            @if ($typeForm == 'edit') {{ $model->to_area_id == $item->id ? 'selected' : '' }} @endif>
                            {{ json_decode($item->name, true)[app()->getLocale()] }}</option>
                    @endforeach
                @endif
            </select>
            @error('Shipment.to_area_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
</div>
<hr>
<div class="row mb-4">
    <div hidden class="col-md-6">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.payment_type') }}</label>
            <select class="form-control kt-select2 payment-type" id="payment_type" name="Shipment[payment_type]"
                data-control="select2" data-placeholder="{{ __('cargo::view.payment_type') }}"
                data-allow-clear="true">
                {{-- <option @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_payment_type') == '1') selected @endif value="1"
                            {{ old('Shipment.payment_type') == 1 ? 'selected' : '' }}
                            @if ($typeForm == 'edit')
                                {{ $model->payment_type == 1 ? 'selected' : '' }}
                            @endif
                        >{{ __('cargo::view.postpaid') }}</option> --}}
                <option selected value="2">{{ __('cargo::view.prepaid') }}</option>
            </select>

        </div>
    </div>
    <div class="col-md-6">
        <div hidden class="form-group">
            <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.payment_method') }}</label>
            <select
                class="form-control kt-select2 payment-method @error('Shipment.payment_method_id') is-invalid @enderror""
                id="payment_method_id" name="Shipment[payment_method_id]" data-control="select2"
                data-placeholder="{{ __('cargo::view.payment_method') }}" data-allow-clear="true">
                @foreach ($paymentSettings as $key => $gateway)
                    {
                    @if ($gateway)
                        <option value="{{ $key }}" @if (Modules\Cargo\Entities\ShipmentSetting::getVal('def_payment_method') == $key) selected @endif
                            {{ old('Shipment.payment_method_id') == $key ? 'selected' : '' }}
                            @if ($typeForm == 'edit') {{ $model->payment_method_id == $key ? 'selected' : '' }} @endif
                            {{ $key == 'cash_payment' ? 'selected' : '' }}>{{ $key }}</option>
                    @endif
                @endforeach
            </select>
            @error('Shipment.payment_method_id')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.order_id') }}</label>
            <input type="text" placeholder="{{ __('cargo::view.order_id') }}" name="Shipment[order_id]"
                class="form-control @error('Shipment.order_id') is-invalid @enderror"
                value="{{ old('Shipment.order_id', isset($model) ? $model->order_id : '') }}" />
        </div>
        @error('Shipment.order_id')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
        @error('Shipment.order_id')
            <div class="invalid-feedback">
                {{ $message }}
            </div>
        @enderror
    </div>
    <div hidden class="col-md-12">
        <div class="form-group">
            <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.attachments') }}</label>

            @if (isset($model))
                <x-media-library-collection name="image" :model="$model" collection="attachments"
                    rules="mimes:jpg,jpeg,png,gif,bmp,svg,webp" />
            @else
                <x-media-library-attachment multiple name="image" rules="mimes:jpg,jpeg,png,gif,bmp,svg,webp" />
            @endif
        </div>
    </div>
</div>

<div class="row mb-6">
    <!--begin::Label-->
    <label class="col-form-label fw-bold fs-6 ">{{ __('Comment') }}</label>
    <!--end::Label-->

    <!--begin::Input group-->
    <div class="col-lg-12 fv-row">
        <div class="input-group mb-4">
            <textarea name="Shipment[comment]"  class="form-control form-control-lg  @error('Shipment.comment') is-invalid @enderror">{{ old('comment', isset($model) ? $model->comment : '') }}</textarea>
            @error('Shipment.comment')
                <div class="invalid-feedback">
                    {{ $message }}
                </div>
            @enderror
        </div>
    </div>
    <!--end::Input group-->
</div>
<hr>

<div id="kt_repeater_1">
    <div class="row" id="">
        <h2 class="text-left">{{ __('cargo::view.package_info') }}</h2>
        <div data-repeater-list="Package" class="col-lg-12">
            @php
                // For edit mode, get existing packages, for create mode use empty array
                $existingPackages = ($typeForm == 'edit' && isset($model))
                    ? Modules\Cargo\Entities\PackageShipment::where('shipment_id', $model->id)->get()
                    : collect([]);

                // If no existing packages, create one empty package for both modes
                if ($existingPackages->isEmpty()) {
                    $existingPackages = collect([(object)[
                        'package_id' => Modules\Cargo\Entities\ShipmentSetting::getVal('def_package_type'),
                        'description' => old('description', ''),
                        'qty' => old('qty', 1),
                        'weight' => old('weight', 0.5),
                        'length' => old('length', 1),
                        'width' => old('width', 1),
                        'height' => old('height', 1)
                    ]]);
                }
            @endphp

            @foreach ($existingPackages as $pack)
                <div data-repeater-item class="row align-items-center"
                    style="margin-top: 15px;padding-bottom: 15px;padding-top: 15px;border-top:1px solid #ccc;border-bottom:1px solid #ccc;">

                    <div class="col-md-3">
                        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.package_type') }}</label>
                        <select class="form-control package-type-select" name="package_id">
                            <option></option>
                            @foreach ($packages as $package)
                                <option value="{{ $package->id }}"
                                    {{ ($pack->package_id ?? '') == $package->id ? 'selected' : '' }}>
                                    {{ json_decode($package->name, true)[app()->getLocale()] }}</option>
                            @endforeach
                        </select>
                        <div class="mb-2 d-md-none"></div>
                    </div>

                    <div class="col-md-3">
                        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.description') }}</label>
                        <input type="text" placeholder="{{ __('cargo::view.description') }}"
                            class="form-control" name="description"
                            value="{{ $pack->description ?? '' }}">
                        <div class="mb-2 d-md-none"></div>
                    </div>

                    <div class="col-md-3">
                        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.quantity') }}</label>
                        <input class="form-control kt_touchspin_qty" placeholder="{{ __('cargo::view.quantity') }}"
                            type="number" min="1" name="qty"
                            value="{{ $pack->qty ?? 1 }}" />
                        <div class="mb-2 d-md-none"></div>
                    </div>

                    <div class="col-md-3">
                        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.weight') }}</label>
                        <input type="number" min="0.5" step="0.5" placeholder="{{ __('cargo::view.weight') }}"
                            name="weight" class="form-control weight-listener kt_touchspin_weight"
                            onchange="calcTotalWeight()"
                            value="{{ $pack->weight ?? 0.5 }}" />
                        <div class="mb-2 d-md-none"></div>
                    </div>

                    <div class="col-md-12" style="margin-top: 10px;">
                        <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.dimensions_length_width_height') }}</label>
                    </div>

                    <div class="col-md-2">
                        <input class="form-control dimensions_r" type="number" min="1"
                            placeholder="{{ __('cargo::view.length') }}" name="length"
                            value="{{ $pack->length ?? 1 }}" />
                    </div>

                    <div class="col-md-2">
                        <input class="form-control dimensions_r" type="number" min="1"
                            placeholder="{{ __('cargo::view.width') }}" name="width"
                            value="{{ $pack->width ?? 1 }}" />
                    </div>

                    <div class="col-md-2">
                        <input class="form-control dimensions_r" type="number" min="1"
                            placeholder="{{ __('cargo::view.height') }}" name="height"
                            value="{{ $pack->height ?? 1 }}" />
                    </div>

                    <div class="col-md-4">
                        <div>
                            <a href="javascript:;" data-repeater-delete=""
                                class="btn btn-sm font-weight-bolder btn-light-danger delete_item">
                                <i class="la la-trash-o"></i>{{ __('cargo::view.delete') }}
                            </a>
                        </div>
                    </div>

                </div>
            @endforeach
        </div>
    </div>
    <div class="form-group row">
        <div class="">
            <label class="text-right col-form-label">{{ __('cargo::view.add_package') }}</label>
            <div>
                <a href="javascript:;" data-repeater-create=""
                    class="btn btn-sm font-weight-bolder btn-light-primary">
                    <i class="la la-plus"></i>{{ __('cargo::view.add_package') }}
                </a>
            </div>
        </div>
    </div>
    <hr>
    @if ($typeForm == 'edit')
        <div class="row">
            <div hidden class="col-md-6">
                <div class="form-group mb-4">
                    <label>{{ __('cargo::view.tax_duty') }}:</label>
                    <input id="kt_touchspin_2" type="text" @if ($user_role == $auth_client) disabled @endif
                        class="form-control" value="{{ old('Shipment.tax', isset($model) ? $model->tax : '') }}"
                        name="Shipment[tax]" />
                </div>
            </div>
            <div hidden class="col-md-6">
                <div class="form-group mb-4">
                    <label>{{ __('cargo::view.insurance') }}:</label>
                    <input id="kt_touchspin_2_2" type="text" @if ($user_role == $auth_client) disabled @endif
                        class="form-control"
                        value="{{ old('Shipment.insurance', isset($model) ? $model->insurance : '') }}"
                        name="Shipment[insurance]" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-4">
                    @if ($is_def_mile_or_fees == '2')
                        <label>{{ __('cargo::view.shipping_cost') }}:</label>
                    @elseif($is_def_mile_or_fees == '1')
                        <label>{{ __('cargo::view.mile_cost') }}:</label>
                    @endif
                    <input id="kt_touchspin_3_shipping_cost" type="text"
                        @if ($user_role == $auth_client) disabled @endif class="form-control"
                        value="{{ old('Shipment.shipping_cost', isset($model) ? $model->shipping_cost : '') }}"
                        name="Shipment[shipping_cost]" />
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group mb-4">
                    @if ($is_def_mile_or_fees == '2')
                        <label>{{ __('cargo::view.return_cost') }}:</label>
                    @elseif($is_def_mile_or_fees == '1')
                        <label>{{ __('cargo::view.return_mile_cost') }}:</label>
                    @endif
                    <input id="kt_touchspin_3_3" type="text" @if ($user_role == $auth_client) disabled @endif
                        class="form-control"
                        value="{{ old('Shipment.return_cost', isset($model) ? $model->return_cost : '') }}"
                        name="Shipment[return_cost]" />
                </div>
            </div>
        </div>
        <hr>
    @endif
    <div class="row">
        <div class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.amount_to_be_collected') }}</label>
                <input id="kt_touchspin_3" placeholder="{{ __('cargo::view.amount_to_be_collected') }}"
                    type="number" min="0"
                    class="form-control @error('Shipment.amount_to_be_collected') is-invalid @enderror"
                    value="{{ old('Shipment.amount_to_be_collected', isset($model) ? $model->amount_to_be_collected : 0) }}"
                    name="Shipment[amount_to_be_collected]" />
                @error('Shipment.amount_to_be_collected')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
        <div class="col-md-6">
            <div class="form-group">
                <label class="col-form-label fw-bold fs-6 required">{{ __('cargo::view.total_weight') }}</label>
                <input id="kt_touchspin_4" placeholder="{{ __('cargo::view.total_weight') }}" type="text"
                    min="0.5"
                    value="{{ old('Shipment.total_weight', isset($model) ? $model->total_weight : 0.5) }}"
                    class="form-control total-weight kt_touchspin_weight @error('Shipment.total_weight') is-invalid @enderror"
                    name="Shipment[total_weight]" />
                @error('Shipment.total_weight')
                    <div class="invalid-feedback">
                        {{ $message }}
                    </div>
                @enderror
            </div>
        </div>
    </div>

</div>
{{--
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-form-label fw-bold fs-6">{{ __('cargo::view.delivery_time') }}</label>
                        <select class="form-control kt-select2 delivery-time @error('Shipment.delivery_time') is-invalid @enderror" id="delivery_time" name="Shipment[delivery_time]">
                            @foreach ($deliveryTimes as $deliveryTime)
                                <option value="{{$deliveryTime->id}}"
                                    {{ old('Shipment.delivery_time') == $deliveryTime->id ? 'selected' : '' }}
                                    @if ($typeForm == 'edit')
                                        {{ $model->delivery_time == $deliveryTime->id ? 'selected' : '' }}
                                    @endif
                                >{{json_decode($deliveryTime->name, true)[app()->getLocale()]}}</option>
                            @endforeach
                        </select>
                        @error('Shipment.delivery_time')
                            <div class="invalid-feedback">
                                {{ $message }}
                            </div>
                        @enderror
                    </div>
                </div>
            </div>
            --}}
<div class="mb-0 text-right form-group">

    <!-- Button trigger modal -->
    <button type="button" class="btn btn-sm btn-primary d-none" data-toggle="modal"
        data-target="#exampleModalCenter" id="modal_open">
        {{ __('cargo::view.save') }}
    </button>

    <!-- Modal -->
    <div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog"
        aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLongTitle">{{ __('cargo::view.estimation_cost') }}</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" id="modal_close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="text-left modal-body">
                    <div class="row">
                        @if ($is_def_mile_or_fees == '2')
                            <div class="col-6">{{ __('cargo::view.shipping_cost') }} :</div>
                            <div class="col-6" id="shipping_cost"></div>
                        @elseif($is_def_mile_or_fees == '1')
                            <div class="col-6">{{ __('cargo::view.mile_cost') }} :</div>
                            <div class="col-6" id="mile_cost"></div>
                        @endif
                    </div>
                    <div class="row">
                        <div class="col-6">{{ __('cargo::view.tax_duty') }} :</div>
                        <div class="col-6" id="tax_duty"></div>
                    </div>
                    <div class="row">
                        <div class="col-6">{{ __('cargo::view.insurance') }} :</div>
                        <div class="col-6" id="insurance"></div>
                    </div>
                    <div class="row">
                        @if (Modules\Cargo\Entities\ShipmentSetting::getVal('is_def_mile_or_fees') == '2')
                            <div class="col-6">{{ __('cargo::view.return_cost') }} :</div>
                            <div class="col-6" id="return_cost"></div>
                        @elseif(Modules\Cargo\Entities\ShipmentSetting::getVal('is_def_mile_or_fees') == '1')
                            <div class="col-6">{{ __('cargo::view.return_mile_cost') }} :</div>
                            <div class="col-6" id="return_mile_cost"></div>
                        @endif
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-6">{{ __('cargo::view.total_cost') }} :</div>
                        <div class="col-6" id="total_cost"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary"
                        data-dismiss="modal">{{ __('cargo::view.close') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('cargo::view.save') }}</button>
                </div>
            </div>
        </div>
    </div>
</div>

</div>
</div>

{{-- Inject styles --}}
@section('styles')
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.css"
        integrity="sha512-g4B+TyvVE4aa0Y1SgjMHnT/4M4IRl8jnG3Ha9ebg8VhLyrfaAqL5AHDh7zo0/ZdES57Y1E7wvWwsDzX806b1Gw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link rel="stylesheet"
        href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.min.css"
        integrity="sha512-0GlDFjxPsBIRh0ZGa2IMkNT54XGNaGqeJQLtMAw6EMEDQJ0WqpnU6COVA91cUS0CeVA5HtfBfzS9rlJR3bPMyw=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="http://ajax.googleapis.com/ajax/libs/jqueryui/1.8/themes/base/jquery-ui.css" rel="Stylesheet"
        type="text/css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/css/intlTelInput.min.css"
        crossorigin="anonymous" />
    <style>
        .input-group .iti--allow-dropdown,
        .phone_input,
        .iti {
            width: 100% !important;
        }

        /* Chrome, Safari, Edge, Opera */
        input::-webkit-outer-spin-button,
        input::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        /* Firefox */
        input[type=number] {
            -moz-appearance: textfield;
        }
    </style>
@endsection

{{-- Inject Scripts --}}
@push('js-component')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.min.js"
        integrity="sha512-foIijUdV0fR0Zew7vmw98E6mOWd9gkGWQBWaoA1EOFAx+pY+N8FmmtIYAVj64R98KeD2wzZh1aHK0JSpKmRH8w=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.repeater/1.2.1/jquery.repeater.js"
        integrity="sha512-bZAXvpVfp1+9AUHQzekEZaXclsgSlAeEnMJ6LfFAvjqYUVZfcuVXeQoN5LhD7Uw0Jy4NCY9q3kbdEXbwhZUmUQ=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.min.js"
        integrity="sha512-0hFHNPMD0WpvGGNbOaTXP0pTO9NkUeVSqW5uFG2f5F9nKyDuHE3T4xnfKhAhnAZWZIO/gBLacwVvxxq0HuZNqw=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-touchspin/4.3.0/jquery.bootstrap-touchspin.js"
        integrity="sha512-k59zBVzm+v8h8BmbntzgQeJbRVBK6AL1doDblD1pSZ50rwUwQmC/qMLZ92/8PcbHWpWYeFaf9hCICWXaiMYVRg=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.3/umd/popper.min.js"
        integrity="sha384-vFJXuSJphROIrBnz7yo7oB41mKfc8JzQZiCq4NCceLEaO4IHwicKwpJf9c9IpFgh" crossorigin="anonymous">
    </script>
    <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0-beta.2/js/bootstrap.min.js"
        integrity="sha384-alpBpkh1PFOepccYVYDB4do5UnbKysX5WZXm3XxPqe5iKTfUKjNkCk9SaVuEZflJ" crossorigin="anonymous">
    </script>
    <script src="{{ asset('assets/global/js/jquery.geocomplete.js') }}"></script>
    <script src="//maps.googleapis.com/maps/api/js?libraries=places&key={{ $google_map_key }}"></script>
    <!-- REQUIRED CDN  -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery.inputmask/5.0.8-beta.1/inputmask.js"
        integrity="sha512-aSxEzzrnqlqgASdjAelu/V291nzZNygMSFMJ0h4PFQ+uwdEz6zKkgsIMbcv0O0ZPwFRNPFWssY7gcL2gZ6/t9A=="
        crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/intlTelInput.min.js"
        crossorigin="anonymous"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/intl-tel-input/17.0.8/js/utils.js" crossorigin="anonymous"></script>


    <script>
        // Map Address For Receiver
        $('.address-receiver').each(function() {
            var address = $(this);
            address.geocomplete({
                map: ".map_canvas.map-receiver",
                mapOptions: {
                    zoom: 8,
                    center: {
                        lat: -34.397,
                        lng: 150.644
                    },
                },
                markerOptions: {
                    draggable: true
                },
                details: ".location-receiver",
                detailsAttribute: 'data-receiver',
                autoselect: true,
                restoreValueAfterBlur: true,
            });
            address.bind("geocode:dragged", function(event, latLng) {
                $("input[data-receiver=lat]").val(latLng.lat());
                $("input[data-receiver=lng]").val(latLng.lng());
            });
        });

        // Map Address For Client
        $('.address-client').each(function() {
            var address = $(this);
            address.geocomplete({
                map: ".map_canvas.map-client",
                mapOptions: {
                    zoom: 8,
                    center: {
                        lat: -34.397,
                        lng: 150.644
                    },
                },
                markerOptions: {
                    draggable: true
                },
                details: ".location-client",
                detailsAttribute: 'data-client',
                autoselect: true,
                restoreValueAfterBlur: true,
            });
            address.bind("geocode:dragged", function(event, latLng) {
                $("input[data-client=lat]").val(latLng.lat());
                $("input[data-client=lng]").val(latLng.lng());
            });
        });

        // IF Branches Select Not Have Branches
        @if (auth()->user()->can('create-branches') || $user_role == $admin)
            $('.select-branch').select2().on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('branches.create') }}?redirect=shipments.create"
                    class="btn btn-primary" >+ {{ __('cargo::view.create_new_branch') }}</a>
                    </li>`);
            });
        @endif

        var inputDate = $(`#kt_datepicker_3`);

        // Trigger date picker for Shipping Date
        inputDate.daterangepicker({
            showDropdowns: true,
            singleDatePicker: true,
            autoUpdateInput: false,
            autoclose: true,
            minYear: parseInt(moment().format('YYYY')) - 10,
            maxYear: parseInt(moment().format('YYYY')) + 10,
            todayHighlight: true,
            startDate: new Date(),
            todayBtn: true,
            locale: {
                format: "DD/MM/YYYY",
                cancelLabel: "{{ __('view.cancel') }}",
                applyLabel: "{{ __('view.apply') }}",
                "fromLabel": "{{ __('view.from') }}",
                "toLabel": "{{ __('view.to') }}",
                "customRangeLabel": "{{ __('datepicker.custom_range') }}",
                "weekLabel": "{{ __('datepicker.week_label') }}",
                "daysOfWeek": [
                    "{{ __('datepicker.days_of_week.sunday') }}",
                    "{{ __('datepicker.days_of_week.monday') }}",
                    "{{ __('datepicker.days_of_week.tuesday') }}",
                    "{{ __('datepicker.days_of_week.wednesday') }}",
                    "{{ __('datepicker.days_of_week.thursday') }}",
                    "{{ __('datepicker.days_of_week.friday') }}",
                    "{{ __('datepicker.days_of_week.saturday') }}",
                ],
                "monthNames": [
                    "{{ __('datepicker.month_names.january') }}",
                    "{{ __('datepicker.month_names.february') }}",
                    "{{ __('datepicker.month_names.march') }}",
                    "{{ __('datepicker.month_names.april') }}",
                    "{{ __('datepicker.month_names.may') }}",
                    "{{ __('datepicker.month_names.june') }}",
                    "{{ __('datepicker.month_names.july') }}",
                    "{{ __('datepicker.month_names.august') }}",
                    "{{ __('datepicker.month_names.september') }}",
                    "{{ __('datepicker.month_names.october') }}",
                    "{{ __('datepicker.month_names.november') }}",
                    "{{ __('datepicker.month_names.december') }}",
                ],
            }
        }, cb);

        // call back after choose date
        function cb(start) {
            var apiDate = start ? start.format("YYYY-MM-DD H:m") : '';
            var inputShowDate = start ? (start.format("YYYY-MM-DD")) : '';
            if (start) {
                inputDate.val(inputShowDate);
            }
        }

        // Trigger time picker for Collection Time
        $('#kt_timepicker_3').daterangepicker({
            timePicker: true,
            singleDatePicker: true,
            timePicker24Hour: false,
            timePickerIncrement: 1,
            timePickerSeconds: true,
            startDate: moment(),
            locale: {
                format: 'HH:mm:ss A'
            }
        }).on('show.daterangepicker', function(ev, picker) {
            picker.container.find(".calendar-table").hide();
        });


        // IF Clients Select Not Have Clients
        @if (auth()->user()->can('create-clients') || $user_role == $admin || $user_role == $auth_branch)
            $('.select-client').select2().on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%"
                    class="btn btn-primary create-new-client" >+ {{ __('cargo::view.create_new_client') }}</a>
                    </li>`);
                $(".create-new-client").click(() => {
                    $("#myModal").modal("show");
                    //    $(".create-new-client").hide();
                });
            });
        @endif
        // IF Clients Select Not Have reciver
        @if ($user_role == $admin || $user_role == $auth_branch)
            $('.select-reciver').select2().on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%"
                    class="btn btn-primary reciver-new-client" >+ {{ __('cargo::view.create_new_receiver') }}</a>
                    </li>`);

                // href="{{ route('receivers.create') }}?redirect=shipments.create"
                $(".reciver-new-client").click(() => {
                    $("#myModalReciver").modal("show");

                });
            });
        @endif

        // IF Payments Select Not Have Clients
        @if (auth()->user()->can('payments-settings') || $user_role == $admin)
            $('.payment-method').select2().on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('payments.index') }}?redirect=shipments.create"
                    class="btn btn-primary" >+ {{ __('cargo::view.add_payment') }}</a>
                    </li>`);
            });
        @endif


        // Set Client Phone After Select Client
        $('.select-client').change(function() {
            var client_phone = $(this).find(':selected').data('phone');
            var client_country_code = $(this).find(':selected').data('country');

            $("#client_phone").val(client_phone);
            $("#country_code").val(client_country_code);

        })
        // Set Receiver Phone After Select Receiver
        $('.select-reciver').change(function() {
            var client_phone = $(this).find(':selected').data('phone');
            var client_country_code = $(this).find(':selected').data('country');
            var client_reciver_address = $(this).find(':selected').data('address');

            // Remove country code prefix if it exists in the phone number
            if (client_phone && client_country_code) {
                var cleanPhone = client_phone.toString().replace(client_country_code, '').replace(/^\+/, '');
                $("#reciver_phone").val(cleanPhone);
            } else {
                $("#reciver_phone").val(client_phone);
            }

            $("#reciver_country_code").val(client_country_code || '+20');
            $("#reciver_address").val(client_reciver_address);

        })

        // Get Client Id
        function selectIdTriggered() {
            getClientAddresses(document.getElementById("client-id").value);
        }

        $('.select-address').select2({
            placeholder: "{{ __('cargo::view.choose_client_first') }}",
        })

        // Ajax Get Client Addresses By Id
        function getClientAddresses(client_id) {
            var id = client_id;

            $.get("{{ route('ajax-get-client-addresses-ajax') }}?client_id=" + id, function(data) {
                if (data.length != 0) {

                    $('select[name ="Shipment[client_address]"]').empty();
                    //$('select[name ="Shipment[client_address]"]').append('<option value=""></option>');
                    for (let index = 0; index < data.length; index++) {
                        const element = data[index];
                        var old_client_address = {{ old('Shipment.client_address') ? old('Shipment.client_address') : 'null' }};
                        var edit_client_address = {{ isset($model) ? $model->client_address : 'null' }};
                        if (element['is_default'] == 1 || old_client_address == element['id'] ||
                            edit_client_address == element['id']) {
                            $('select[name ="Shipment[client_address]"]').append('<option selected value="' +
                                element['id'] + '">' + element['address'] + '</option>');
                        } else {
                            $('select[name ="Shipment[client_address]"]').append('<option value="' + element['id'] +
                                '">' + element['address'] + '</option>');
                        }

                    }

                    $('.select-address').select2({
                        placeholder: "{{ __('cargo::view.choose_address') }}",
                    })
                    @if (
                        $user_role == $admin ||
                            $user_role == $auth_client ||
                            $user_role == $auth_branch ||
                            auth()->user()->can('manage-clients'))
                        .on('select2:open', () => {

                            $('.toRemoveLi').remove();

                            // $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;' class='toRemoveLi'><a style="width: 100%" onclick="openAddressDiv()"
                        //     class="btn btn-primary" >+ {{ __('cargo::view.create_new_address') }}</a>
                        //     </li>`);
                        });
                    @endif
                } else {
                    $('select[name ="Shipment[client_address]"]').empty();
                    $('.select-address').select2({
                        placeholder: "{{ __('cargo::view.no_addresses_found') }}",
                    })
                    @if ($user_role == $admin || $user_role == $auth_client || auth()->user()->can('manage-clients'))
                        .on('select2:open', () => {

                            $('.toRemoveLi').remove();

                            $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;' class='toRemoveLi'><a style="width: 100%" onclick="openAddressDiv()"
                                class="btn btn-primary" >+ {{ __('cargo::view.create_new_address') }}</a>
                                </li>`);
                        });
                    @endif
                }
            });
        }

        // Ajax Get One Client Addresses By Id
        // $('#client-addresses').change(function() {
        //     var id = $(this).val();
        //     $.get("{{ route('client.get.one.address') }}?address_id=" + id, function(data) {
        //         $("#change-country").val(data[0]['country_id']).change();
        //         setTimeout(function() {
        //             $("#change-state-from").val(data[0]['state_id']).change();
        //             if (data[0]['area_id'] != null || data[0]['area_id'] != "") {
        //                 setTimeout(function() {
        //                     $("#from_area_id").val(data[0]['area_id']).change();
        //                 }, 800);
        //             }
        //         }, 800);
        //     });
        // });

        @if (old('Shipment.client_id'))
            getClientAddresses({{ old('Shipment.client_id') }});
        @endif
        // Ajax Get Address With Client logged in
        @if ($user_role == $auth_client)
            getClientAddresses({{ $userClient->id }});
        @endif

        // Ajax Add New Address For Client
        function AddNewClientAddress() {
            @if ($user_role == $auth_client)
                var id = {{ $userClient->id }};
            @else
                var id = document.getElementById("client-id").value;
            @endif
            var address = document.getElementsByName("client_address")[0].value;
            var country = $('select[name ="country_id"]').val();
            var state = $('select[name ="state_id"]').val();
            var area = $('select[name ="area_id"]').val();

            @if ($googleMap)
                var client_street_address_map = document.getElementsByName("client_street_address_map")[0].value;
                var client_lat = document.getElementsByName("client_lat")[0].value;
                var client_lng = document.getElementsByName("client_lng")[0].value;
                var client_url = document.getElementsByName("client_url")[0].value;
                if (address != "" && country != "" && state != "" && address != null && country != null && state != null) {
                    $.post("{{ route('client.add.new.address') }}", {
                        _token: "{{ csrf_token() }}",
                        client_id: parseInt(id),
                        address: address,
                        client_street_address_map: client_street_address_map,
                        client_lat: client_lat,
                        client_lng: client_lng,
                        client_url: client_url,
                        country: country,
                        state: state,
                        area: area
                    }, function(data) {
                        $('select[name ="Shipment[client_address]"]').empty();
                        for (let index = 0; index < data.length; index++) {
                            const element = data[index];
                            $('select[name ="Shipment[client_address]"]').append('<option value="' + element['id'] +
                                '">' + element['address'] + '</option>');
                        }
                        document.getElementsByName("client_address")[0].value = "";
                        document.getElementsByName("client_street_address_map")[0].value = "";
                        document.getElementsByName("client_lat")[0].value = "";
                        document.getElementsByName("client_lng")[0].value = "";
                        document.getElementsByName("client_url")[0].value = "";
                    });
                } else {
                    Swal.fire("{{ __('cargo::view.please_enter_all_reqired_fields') }}", "", "error");
                }
            @else
                if (address != "" && country != "" && state != "" && address != null && country != null && state != null) {
                    $.post("{{ route('client.add.new.address') }}", {
                        _token: "{{ csrf_token() }}",
                        client_id: parseInt(id),
                        address: address,
                        country: country,
                        state: state,
                        area: area
                    }, function(data) {
                        $('select[name ="Shipment[client_address]"]').empty();
                        for (let index = 0; index < data.length; index++) {
                            const element = data[index];
                            $('select[name ="Shipment[client_address]"]').append('<option value="' + element['id'] +
                                '">' + element['address'] + '</option>');
                        }
                        document.getElementsByName("client_address")[0].value = "";
                        var country = $('select[name ="country_id"]').val();
                        var state = $('select[name ="state_id"]').val();
                        var area = $('select[name ="area_id"]').val();
                    });
                } else {
                    Swal.fire("{{ __('cargo::view.please_enter_all_reqired_fields') }}", "", "error");
                }
            @endif
        }

        function openAddressDiv() {
            $("#show_address_div").slideDown("slow", function() {
                // Animation complete.
            });
        }

        function closeAddressDiv() {
            $("#show_address_div").slideUp("slow", function() {
                // Animation complete.
            });
        }

        $('#change-country-client-address').change(function() {
            var id = $(this).val();
            $.get("{{ route('ajax.getStates') }}?country_id=" + id, function(data) {
                $('select[name ="state_id"]').empty();
                $('select[name ="state_id"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];

                    $('select[name ="state_id"]').append('<option value="' + element['id'] + '">' + element[
                        'name'] + '</option>');
                }
            });
            @if (auth()->user()->can('add-covered-countries') || $user_role == $admin)
                $('.select-state').on('select2:open', () => {
                    $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('countries.index') }}?redirect=shipments.create"
                        class="btn btn-primary" >{{ __('cargo::view.manage') }} {{ __('cargo::view.covered_countries') }}</a>
                        </li>`);
                });
            @endif
        });



        $('#change-state-client-address').change(function() {
            var id = $(this).val();
            $.get("{{ route('ajax.getAreas') }}?state_id=" + id, function(data) {
                $('select[name ="area_id"]').empty();
                $('select[name ="area_id"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    $('select[name ="area_id"]').append('<option value="' + element['id'] + '">' + JSON
                        .parse(element['name'], true)[`{{ app()->getLocale() }}`] + '</option>');
                }
            });

            @if (auth()->user()->can('create-areas') || $user_role == $admin)
                $('.select-area').on('select2:open', () => {
                    $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('areas.create') }}?redirect=shipments.create"
                        class="btn btn-primary" >{{ __('cargo::view.areas_management') }}</a>
                        </li>`);
                });
            @endif
        });

        $('.select-country').select2({
            placeholder: "{{ __('cargo::view.choose_country') }}",
            width: '100%'
        })
        @if (auth()->user()->can('add-covered-countries') || $user_role == $admin)
            .on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('countries.index') }}?redirect=shipments.create"
                    class="btn btn-primary" >{{ __('cargo::view.manage') }} {{ __('cargo::view.covered_countries') }}</a>
                    </li>`);
            });
        @endif

        $('.select-state').select2({
            placeholder: "{{ __('cargo::view.choose_region') }}",
            width: '100%'
        })
        @if (auth()->user()->can('add-covered-regions') || $user_role == $admin)
            .on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('countries.index') }}?redirect=shipments.create"
                    class="btn btn-primary" >{{ __('cargo::view.manage') }} {{ __('cargo::view.covered_regions') }}</a>
                    </li>`);
            });
        @endif

        $('.select-area').select2({
            placeholder: "{{ __('cargo::view.choose_area') }}",
            width: '100%'
        })
        @if (auth()->user()->can('manage-areas') || $user_role == $admin)
            .on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('areas.index') }}?redirect=shipments.create"
                    class="btn btn-primary" >{{ __('cargo::view.manage') }} {{ __('cargo::view.areas') }}</a>
                    </li>`);
            });
        @endif

        function changeCountry(id) {
            $.get("{{ route('ajax.getStates') }}?country_id=" + id, function(data) {
                $('select[name ="Shipment[from_state_id]"]').empty();
                $('select[name ="Shipment[from_state_id]"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];

                    var old_from_state = {{ old('Shipment.from_state_id') ? old('Shipment.from_state_id') : (isset($model) ? $model->from_state_id : 'null') }};
                    if (old_from_state == element['id']) {
                        $('select[name ="Shipment[from_state_id]"]').append('<option selected value="' + element[
                            'id'] + '">' + element['name'] + '</option>');
                    } else {
                        $('select[name ="Shipment[from_state_id]"]').append('<option value="' + element['id'] +
                            '">' + element['name'] + '</option>');
                    }
                }
            });
        }
        $('#change-country').change(function() {
            var id = $(this).val();
            changeCountry(id)
        });
        changeCountry(65)
        @if (old('Shipment.from_country_id'))
            changeCountry({{ old('Shipment.from_country_id') }})
        @endif

        function changeCountryTo(id) {
            $.get("{{ route('ajax.getStates') }}?country_id=" + id, function(data) {
                $('select[name ="Shipment[to_state_id]"]').empty();
                $('select[name ="Shipment[to_state_id]"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    var old_to_state = {{ old('Shipment.to_state_id') ? old('Shipment.to_state_id') : (isset($model) ? $model->to_state_id : 'null') }};
                    if (old_to_state == element['id']) {
                        $('select[name ="Shipment[to_state_id]"]').append('<option selected value="' + element[
                            'id'] + '">' + element['name'] + '</option>');
                    } else {
                        $('select[name ="Shipment[to_state_id]"]').append('<option value="' + element['id'] + '">' +
                            element['name'] + '</option>');
                    }
                }
            });
        }
        $('#change-country-to').change(function() {
            var id = $(this).val();
            changeCountryTo(id);
        });

        changeCountryTo(65);

        @if (old('Shipment.to_country_id'))
            changeCountryTo({{ old('Shipment.to_country_id') }})
        @endif

        function changeStateFrom(id) {
            $.get("{{ route('ajax.getAreas') }}?state_id=" + id, function(data) {
                $('select[name ="Shipment[from_area_id]"]').empty();
                $('select[name ="Shipment[from_area_id]"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    var old_from_area = {{ old('Shipment.from_area_id') ? old('Shipment.from_area_id') : (isset($model) ? $model->from_area_id : 'null') }};
                    if (old_from_area == element['id']) {
                        $('select[name ="Shipment[from_area_id]"]').append('<option selected value="' + element[
                                'id'] + '">' + JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] +
                            '</option>');
                    } else {
                        $('select[name ="Shipment[from_area_id]"]').append('<option value="' + element['id'] +
                            '">' + JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] + '</option>'
                        );
                    }
                }
            });
        }
        $('#change-state-from').change(function() {
            var id = $(this).val();
            changeStateFrom(id);
        });
        @if (old('Shipment.from_state_id'))
            changeStateFrom({{ old('Shipment.from_state_id') }})
        @endif

        function changeStateTo(id) {
            $.get("{{ route('ajax.getAreas') }}?state_id=" + id, function(data) {
                $('select[name ="Shipment[to_area_id]"]').empty();
                $('select[name ="Shipment[to_area_id]"]').append('<option value=""></option>');
                for (let index = 0; index < data.length; index++) {
                    const element = data[index];
                    var old_to_area = {{ old('Shipment.to_area_id') ? old('Shipment.to_area_id') : (isset($model) ? $model->to_area_id : 'null') }};
                    if (old_to_area == element['id']) {
                        $('select[name ="Shipment[to_area_id]"]').append('<option selected value="' + element[
                                'id'] + '">' + JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] +
                            '</option>');
                    } else {
                        $('select[name ="Shipment[to_area_id]"]').append('<option value="' + element['id'] + '">' +
                            JSON.parse(element['name'], true)[`{{ app()->getLocale() }}`] + '</option>');
                    }
                }
            });
        }




        // $("#change-state-to").change(function() {
        //     console.log("innn");

        //     var id = $(this).val();

        //     changeStateTo(id);
        // });
        $(document).on("change", "#change-state-to", function() {
            console.log("innn");

            var id = $(this).val();
            changeStateTo(id);
        });
        console.log("oooooooooooooooooooooooooo");

        @if (old('Shipment.to_state_id'))
            changeStateTo({{ old('Shipment.to_state_id') }})
        @endif

        //Package Types Repeater

        $('#kt_repeater_1').repeater({
            initEmpty: false,

            show: function() {
                $(this).slideDown();

                // Initialize TouchSpin for new package items
                $(this).find('.dimensions_r').TouchSpin({
                    buttondown_class: 'btn btn-secondary',
                    buttonup_class: 'btn btn-secondary',

                    min: 1,
                    max: 1000000000,
                    stepinterval: 50,
                    maxboostedstep: 10000000,
                    initval: 1,
                });

                $(this).find('.kt_touchspin_weight').TouchSpin({
                    buttondown_class: 'btn btn-secondary',
                    buttonup_class: 'btn btn-secondary',

                    step: 0.5,
                    min: 0.5,
                    boostat: 5,
                    max: 1000000000,
                    stepinterval: 50,
                    maxboostedstep: 50,
                    initval: 0.5,
                    decimals: 1,
                    prefix: 'Kg'
                });

                $(this).find('.kt_touchspin_qty').TouchSpin({
                    buttondown_class: 'btn btn-secondary',
                    buttonup_class: 'btn btn-secondary',

                    min: 1,
                    max: 1000000000,
                    stepinterval: 50,
                    maxboostedstep: 10000000,
                    initval: 1,
                });

                // Initialize Select2 for package type dropdown
                $(this).find('.package-type-select').select2({
                    placeholder: "{{ __('cargo::view.choose_package_type') }}",
                    allowClear: true
                });

                // Add change event listener for weight calculation
                $(this).find('.weight-listener').on('change keyup', function() {
                    calcTotalWeight();
                });

                calcTotalWeight();
            },

            hide: function(deleteElement) {
                $(this).slideUp(deleteElement);
                // Recalculate total weight when package is deleted
                setTimeout(function() {
                    calcTotalWeight();
                }, 300);
            },

            // Allow deletion of first item in edit mode
            isFirstItemUndeletable: false
        });

        // Initialize TouchSpin for all existing packages (both create and edit modes)
        setTimeout(function() {
            // Initialize TouchSpin for all existing packages
            $('.dimensions_r').each(function() {
                if (!$(this).hasClass('bootstrap-touchspin-injected')) {
                    $(this).TouchSpin({
                        buttondown_class: 'btn btn-secondary',
                        buttonup_class: 'btn btn-secondary',
                        min: 1,
                        max: 1000000000,
                        stepinterval: 50,
                        maxboostedstep: 10000000,
                        initval: 1,
                    });
                }
            });

            $('.kt_touchspin_weight').each(function() {
                if (!$(this).hasClass('bootstrap-touchspin-injected')) {
                    $(this).TouchSpin({
                        buttondown_class: 'btn btn-secondary',
                        buttonup_class: 'btn btn-secondary',
                        step: 0.5,
                        min: 0.5,
                        boostat: 5,
                        max: 1000000000,
                        stepinterval: 50,
                        maxboostedstep: 50,
                        initval: 0.5,
                        decimals: 1,
                        prefix: 'Kg'
                    });
                }
            });

            $('.kt_touchspin_qty').each(function() {
                if (!$(this).hasClass('bootstrap-touchspin-injected')) {
                    $(this).TouchSpin({
                        buttondown_class: 'btn btn-secondary',
                        buttonup_class: 'btn btn-secondary',
                        min: 1,
                        max: 1000000000,
                        stepinterval: 50,
                        maxboostedstep: 10000000,
                        initval: 1,
                    });
                }
            });

            // Initialize Select2 for package type dropdowns
            $('.package-type-select').each(function() {
                if (!$(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2({
                        placeholder: "{{ __('cargo::view.choose_package_type') }}",
                        allowClear: true
                    });
                }
            });

            // Calculate total weight on page load
            calcTotalWeight();
        }, 100);

        // Handle package deletion
        $('body').on('click', '.delete_item', function() {
            $('.total-weight').val("{{ __('cargo::view.calculated') }}");
            setTimeout(function() {
                calcTotalWeight();
            }, 500);
        });

        // Handle weight changes in packages
        $('body').on('change keyup', '.weight-listener', function() {
            calcTotalWeight();
        });

        // Handle package addition
        $('body').on('click', '[data-repeater-create]', function() {
            setTimeout(function() {
                calcTotalWeight();
            }, 100);
        });

        $('#kt_touchspin_2, #kt_touchspin_2_2').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',

            min: -1000000000,
            max: 1000000000,
            stepinterval: 50,
            maxboostedstep: 10000000,
            prefix: '%'
        });
        $('#kt_touchspin_3, #kt_touchspin_3_shipping_cost, #kt_touchspin_3_3').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',

            min: 0,
            max: 1000000000,
            stepinterval: 50,
            maxboostedstep: 10000000,
            step: 0.001,
            decimals: 3,
            prefix: '$'
        });
        $('.kt_touchspin_weight').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',

            step: 0.5,
            min: 0.5,
            decimals: 1,
            boostat: 5,
            min: 0.5,
            max: 1000000000,
            stepinterval: 50,
            maxboostedstep: 50,
            initval: 0.5,
            prefix: 'Kg'
        });
        $('.kt_touchspin_qty').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',

            min: 1,
            max: 1000000000,
            stepinterval: 50,
            maxboostedstep: 10000000,
            initval: 1,
        });
        $('.dimensions_r').TouchSpin({
            buttondown_class: 'btn btn-secondary',
            buttonup_class: 'btn btn-secondary',

            min: 1,
            max: 1000000000,
            stepinterval: 50,
            maxboostedstep: 10000000,
            initval: 1,
        });

        $('.delivery-time').select2({
            placeholder: "{{ __('cargo::view.delivery_time') }}"
        })
        @if (auth()->user()->can('create-delivery-time') || $user_role == $admin)
            .on('select2:open', () => {
                $(".select2-results:not(:has(a))").append(`<li style='list-style: none; padding: 10px;'><a style="width: 100%" href="{{ route('deliveryTime.create') }}?redirect=shipments.create"
                    class="btn btn-primary" >{{ __('cargo::view.manage') }} {{ __('cargo::view.delivery_time') }}</a>
                    </li>`);
            });
        @endif

        function calcTotalWeight() {
            var elements = $('.weight-listener');
            var sumWeight = 0;
            elements.map(function() {
                sumWeight += parseFloat($(this).val());
            }).get();
            $('.total-weight').val(sumWeight);
        }

        // Initialize weight calculation on page load for both create and edit forms
        $(document).ready(function() {
            // Calculate initial total weight when page loads
            setTimeout(function() {
                calcTotalWeight();
            }, 500);

            // Recalculate when weight inputs change
            $(document).on('input change', '.weight-listener', function() {
                calcTotalWeight();
            });
        });

        function get_estimation_cost() {
            var total_weight = document.getElementById('kt_touchspin_4').value;
            var select_packages = document.getElementsByClassName('package-type-select');

            var from_country_id = document.getElementsByName("Shipment[from_country_id]")[0].value;
            var to_country_id = document.getElementsByName("Shipment[to_country_id]")[0].value;
            var from_state_id = document.getElementsByName("Shipment[from_state_id]")[0].value;
            var to_state_id = document.getElementsByName("Shipment[to_state_id]")[0].value;
            var from_area_id = document.getElementsByName("Shipment[from_area_id]")[0].value;
            var to_area_id = document.getElementsByName("Shipment[to_area_id]")[0].value;
            @if ($user_role == $auth_client)
                var client_id = {{ $userClient->id }};
            @else
                var client_id = document.getElementById("client-id").value;
                console.log(client_id);
            @endif

            var package_ids = [];
            for (let index = 0; index < select_packages.length; index++) {
                if (select_packages[index].value) {
                    package_ids[index] = new Object();
                    package_ids[index]["package_id"] = select_packages[index].value;
                } else {
                    Swal.fire("{{ __('cargo::view.please_select_package_type') }}" + (index + 1), "", "error");
                    return 0;
                }
            }

            var request_data = {
                _token: '{{ csrf_token() }}',

                package_ids: package_ids,
                total_weight: total_weight,
                from_country_id: from_country_id,
                to_country_id: to_country_id,
                from_state_id: from_state_id,
                to_state_id: to_state_id,
                from_area_id: from_area_id,
                to_area_id: to_area_id,
                client_id: client_id,
            };
            $.post('{{ route('shipments.get-estimation-cost') }}', request_data, function(response) {

                if ({{ $is_def_mile_or_fees }} == '2') {
                    document.getElementById("shipping_cost").innerHTML = response.shipping_cost;
                    document.getElementById("return_cost").innerHTML = response.return_cost;
                } else if ({{ $is_def_mile_or_fees }} == '1') {
                    document.getElementById("mile_cost").innerHTML = response.shipping_cost;
                    document.getElementById("return_mile_cost").innerHTML = response.return_cost;
                }
                document.getElementById("tax_duty").innerHTML = response.tax;
                document.getElementById("insurance").innerHTML = response.insurance;
                document.getElementById("total_cost").innerHTML = response.total_cost;
                document.getElementById('modal_open').click();
            });
        }

        {{--
        $(function () {
            let phoneNumbers_1 = $('.phone_input_1'),
                wrong_number = window.wrong_number_msg,
                required_phone = window.required_phone



                phoneNumbers_1.each(function () {
                let self = $(this),
                    input = self[0],
                    type = self.attr('data-type');



                    // initialise plugin
                let iti = window.intlTelInput(input, {
                    separateDialCode: true,
                    utilsScript: window.static_asset_utils_file,
                    initialCountry: "eg",
                    preferredCountries: ["eg","sa","ae","kw"],
                    autoPlaceholder: "aggressive"
                });
                 // Set Client Phone After Select Client
                $('.select-client').change(function(){
                    var client_phone = $(this).find(':selected').data('phone');
                    var client_country_code = $(this).find(':selected').data('country');
                        // Change the country code
                    iti.setNumber(client_country_code + client_phone)
                })
                input.addEventListener("countrychange", function() {
                    $('.phone_input_1').filter(`[data-reflection="${type}"]`).val(iti.getSelectedCountryData().dialCode);
                    $('.country_code_1').val('+'+iti.getSelectedCountryData().dialCode);
                });
                let reset = function() {
                    self.parent().next('.invalid-feedback').remove();
                    self.parent().removeClass('not-valid');
                    self.removeClass("error is-invalid");
                };
                let addError = function(msg) {
                    self.addClass('error is-invalid');
                    self.parent().addClass('not-valid');
                    self.parent().after("<span style='display: block' class=\"invalid-feedback\" role=\"alert\">\n" +
                        " <strong>" + msg + "</strong>\n" +
                        " </span>");
                    return false;
                };
                // on blur: validate
                input.addEventListener('blur', function() {
                    reset();
                    if (self.attr('required')) {
                        if (input.value.trim() == '') {
                            return addError('field is empty')
                        }
                    }
                    if (input.value.trim() && !iti.isValidNumber()) {
                        return addError('reqierd')
                    }
                    // run code if verified
                });
                // on keyup / change flag: reset
                input.addEventListener('change', reset);
                input.addEventListener('keyup', reset);
            })
            $(".number-only").keypress(function(event){
                var ewn = event.which;
                if(ewn >= 48 && ewn <= 57) {
                    return true;
                }
                return false;
            });
            $(".phone-validation").on("submit", function(evt) {
                var phoneField = $(this).find(".phone_input_1");
                if (phoneField.hasClass('error')) {
                    evt.preventDefault();
                    return false
                } else {
                    //do the rest of your validations here
                    $(this).submit();
                }
            });

        });

        $(function () {
            let phoneNumbers_2 = $('.phone_input_2'),
                wrong_number = window.wrong_number_msg,
                required_phone = window.required_phone

                phoneNumbers_2.each(function () {
                let self = $(this),
                    input = self[0],
                    type = self.attr('data-type');
                    // initialise plugin
                let iti = window.intlTelInput(input, {
                    separateDialCode: true,
                    utilsScript: window.static_asset_utils_file,
                    initialCountry: "eg",
                    preferredCountries: ["eg","sa","ae","kw"],
                    autoPlaceholder: "aggressive"
                });
                // Set Receiver Phone After Select Receiver
                $('.select-reciver').change(function(){
                    var client_phone = $(this).find(':selected').data('phone');
                    var client_country_code = $(this).find(':selected').data('country');

                    if (client_phone && client_country_code) {
                        // Remove country code prefix if it exists in the phone number
                        var cleanPhone = client_phone.toString().replace(client_country_code, '').replace(/^\+/, '');
                        // Set the number with country code for intl-tel-input
                        iti.setNumber(client_country_code + cleanPhone);
                    }
                })
                input.addEventListener("countrychange", function() {
                    $('.phone_input_2').filter(`[data-reflection="${type}"]`).val(iti.getSelectedCountryData().dialCode);
                    $('.country_code_2').val('+'+iti.getSelectedCountryData().dialCode);
                });
                let reset = function() {
                    self.parent().next('.invalid-feedback').remove();
                    self.parent().removeClass('not-valid');
                    self.removeClass("error is-invalid");
                };
                let addError = function(msg) {
                    self.addClass('error is-invalid');
                    self.parent().addClass('not-valid');
                    self.parent().after("<span style='display: block' class=\"invalid-feedback\" role=\"alert\">\n" +
                        " <strong>" + msg + "</strong>\n" +
                        " </span>");
                    return false;
                };
                // on blur: validate
                input.addEventListener('blur', function() {
                    reset();
                    if (self.attr('required')) {
                        if (input.value.trim() == '') {
                            return addError('field is empty')
                        }
                    }
                    if (input.value.trim() && !iti.isValidNumber()) {
                        return addError('reqierd')
                    }
                    // run code if verified
                });
                // on keyup / change flag: reset
                input.addEventListener('change', reset);
                input.addEventListener('keyup', reset);
            })

            $(".number-only").keypress(function(event){
                var ewn = event.which;
                if(ewn >= 48 && ewn <= 57) {
                    return true;
                }
                return false;
            });

            $(".phone-validation").on("submit", function(evt) {
                var phoneField = $(this).find(".phone_input_2");
                if (phoneField.hasClass('error')) {
                    evt.preventDefault();
                    return false
                } else {
                    //do the rest of your validations here
                    $(this).submit();
                }
            });
        });
        --}}

        // Ensure full phone numbers are saved on form submission
        $('form').on('submit', function() {
            // Get all intl-tel-input instances
            $('.phone_input_1, .phone_input_2').each(function() {
                var input = this;
                var iti = window.intlTelInputGlobals.getInstance(input);

                if (iti && iti.isValidNumber()) {
                    // Set the full international number
                    $(input).val(iti.getNumber());

                    // Update the corresponding country code field
                    if ($(input).hasClass('phone_input_1')) {
                        $('.country_code_1').val('+' + iti.getSelectedCountryData().dialCode);
                    } else if ($(input).hasClass('phone_input_2')) {
                        $('.country_code_2').val('+' + iti.getSelectedCountryData().dialCode);
                    }
                }
            });
        });
    </script>
@endpush
