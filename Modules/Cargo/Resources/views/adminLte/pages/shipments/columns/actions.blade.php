@php
    $user_role = auth()->user()->role;
    $admin = 1;
    $branch = 3;
    $client = 4;
    $staff = 0;
@endphp

<!-- begin: Btn View Shipment Row -->


{{-- <a href="{{ fr_route('shipments.show_chat', $model->id) }}" class="btn btn-sm btn-secondary btn-action-table"
    data-toggle="tooltip" title="{{ __('Chat') }}">
    <i class="fas fa-comment fa-fw"></i>

</a> --}}

@php
    $unread_messages_count = 0;
    if (auth()->user()->role == $admin || auth()->user()->role == $branch) {
        $unread_messages_count = Modules\Cargo\Entities\ShipmentChat::where('shipment_id', $model->id)
            ->where('admin_is_read', 0)
            ->count();
    } elseif (auth()->user()->role == $client) {
        $unread_messages_count = Modules\Cargo\Entities\ShipmentChat::where('shipment_id', $model->id)
            ->where('provider_is_read', 0)
            ->count();
    }
    // $unread_messages_count = 10;
@endphp


@if (empty($model->close_reason)    )
    <a href="{{ fr_route('shipments.show_chat', $model->id) }}"
        class="btn btn-sm btn-secondary btn-action-table position-relative" data-toggle="tooltip"
        title="{{ __('Chat') }}">
        <i class="fas fa-comment fa-fw"></i>

        @if ($unread_messages_count > 0)
            <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                {{ $unread_messages_count }}
                <span class="visually-hidden">unread messages</span>
            </span>
        @endif
    </a>
@endif




@if (auth()->user()->can('view-shipments') || $user_role == $admin || $user_role == $branch || $user_role == $client)
    <a href="{{ fr_route('shipments.show', $model->id) }}" class="btn btn-sm btn-secondary btn-action-table"
        data-toggle="tooltip" title="{{ __('catgo::view.shipment') }}">
        <i class="fas fa-eye fa-fw"></i>
    </a>
@endif



<!-- end: Btn View profile Row -->

<!-- begin: Btn Edit Row -->
@if (auth()->user()->can('edit-shipments') ||
        $user_role == $admin ||
        $user_role == $branch ||
        ($user_role == $client && $model->status_id == Modules\Cargo\Entities\Shipment::SAVED_STATUS))
    @if ($model->is_withdraw != 1)
        <a href="{{ fr_route('shipments.edit', $model->id) }}" class="btn btn-sm btn-secondary btn-action-table"
            data-toggle="tooltip" title="{{ __('view.edit') }}">
            <i class="fas fa-edit fa-fw"></i>
        </a>
    @endif
@endif



@if (auth()->user()->can('view-shipments') || $user_role == $admin || $user_role == $branch || $user_role == $client)

    @if ($model->status_id == 1)
        <a href="{{ fr_route('shipments.delete', $model->id) }}" class="btn btn-sm btn-secondary btn-action-table"
            data-toggle="tooltip" title="{{ __('catgo::delete.shipment') }}">
            <i class="fas fa-trash fa-fw"></i>
        </a>
    @endif

@endif
<!-- end: Btn Edit Row -->
