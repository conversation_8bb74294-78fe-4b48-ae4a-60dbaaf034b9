<?php

return [

    'covered_places' => 'Covered Places',
    'covered_countries' => 'Covered Countries',
    'all_countries' => 'All Countries',
    'add_covered_regions' => 'Add Covered Regions',
    'covered_regions' => 'Covered Regions',
    'manage' => 'Manage',
    'countries' => 'Countries',

    'covered_states' => 'Covered Regions',
    'all_states' => 'All Regions',

    'areas_management' => 'Areas Management',
    'add_area' => 'Add Area',
    'create_new_area' => 'Create new area',
    'edit_area' => 'Edit Area',
    'area_list' => 'Areas list',
    'selected_areas' => 'selected areas',
    'area_name' => 'Area Name',
    'areas' => 'Areas',
    'area_id' => 'Area ID',

    'country' => 'Country',
    'from_country' => 'From Country',
    'to_country' => 'To Country',
    'region' => 'Region',
    'region_name' => 'Region Name',
    'regions' => 'Regions',
    'from_region' => 'From Region',
    'to_region' => 'To Region',
    'area' => 'Area',
    'from_area' => 'From Area',
    'to_area' => 'To Area',
    'choose_region' => 'Choose Region',
    'choose_country' => 'Choose Country',
    'choose_area' => 'Choose Area',

    'delivery_time' => 'Delivery Time',
    'delivery_times' => 'Delivery Times',
    'add_delivery_time' => 'Add delivery time',
    'create_new_delivery_time' => 'Create new delivery time',
    'edit_delivery_time' => 'Edit delivery time',
    'delivery_time_list' => 'Delivery times list',
    'selected_delivery_times' => 'selected delivery times',

    'edit_profile' => 'Edit Profile',

    'packages' => 'Packages',
    'add_package' => 'Add package',
    'create_new_package' => 'Create new package',
    'edit_package' => 'Edit package',
    'package_list' => 'Packages list',
    'selected_packages' => 'selected packages',
    'facebook' => 'Facebook',
    'website' => 'Website',
    'friend' => 'Friend',
    'sales_team' => 'Sales Team',
    'google' => 'Google',
    'shipment_team' => 'Shipment Team',
    'shipping_rates' => 'Shipping Rates',
    'shipping_settings' => 'Shipping Settings',
    'shipping_general_settings' => 'Shipping General Settings',
    'pickup_mission_done_with_shipment_fees_received' => 'Pickup mission done with shipment fees received ?',
    'yes' => 'Yes',
    'no' => 'No',
    'no_mission_only_has_been_done' => 'No, mission only has been done.',
    'show_register_in_driver_app' => 'Show register in driver app ?',
    'enable_shippment_calc_in_website' => 'Enable Shippment Calculator ?',
    'default_count_for_dashboard_latest_shipment_widget' => 'Default Count for dashboard Latest shipment widget',
    'enable_shipping_date' => 'Enable Shipping date',
    'defult_shipping_date' => 'Defult Shipping date',
    'same_day' => 'Same Day',
    'next_day' => 'Next Day',
    'after' => 'After',
    'days' => 'Days',
    'shipment_prefix' => 'Shipment Prefix',
    'shipment_number_of_digits_in_the_tracking' => 'Shipment Number of digits in the tracking',
    'mission_prefix' => 'Mission Prefix',
    'mission_number_of_digits_in_the_tracking' => 'Mission Number of digits in the tracking',
    'default_shipment_type' => 'Default Shipment Type',
    'Pickup_For_door_to_door_delivery' => 'Pickup (For door to door delivery)',
    'drop_off_For_delivery_package_from_branch_directly' => 'Drop off (For delivery package from branch directly)',
    'default_shipment_code_number_type' => 'Default Shipment Code Number Type',
    'sequential' => 'Sequential',
    'random_recommended_for_security' => 'Random (Recommended, for security)',
    'receiving_mission_confirmation_type' => 'Receiving Mission Confirmation Type',
    'customer_signature' => 'Customer Signature',
    'otp' => 'OTP',
    'without_confirmation' => 'Without Confirmation',
    'to_active_OTP_confirmation_type_config_SMS_gateways_first' => 'To Active OTP Confirmation Type , Config SMS Gateways First',
    'configure_now' => 'Configure Now',
    'default_package_type' => 'Default Package Type',
    'package_type' => 'Package Type',
    'default_branch' => 'Default Branch',
    'default_payment_type' => 'Default Payment Type',
    'payment_type' => 'Payment Type',
    'postpaid' => 'Postpaid',
    'POSTPAID' => 'POSTPAID',
    'prepaid' => 'Prepaid',
    'PREPAID' => 'PREPAID',
    'UNPaid' => 'Un Paid',
    'save' => 'Save',
    'default_payment_method' => 'Default Payment Method',
    'payment_method' => 'Payment Method',
    'please_configure_package_types' => 'Please Configure Package Types',
    'default_pickup_mission_cost' => 'Default Pickup Mission Cost',
    'default_supply_mission_cost' => 'Default Supply Mission Cost',
    'cash' => 'Cash',
    'please_configure_your_covered_countries_and_cities' => 'Please Configure Your covered countries and cities',
    'configure_costs' => 'Configure Costs',
    'configure_selected_countries_costs' => 'Configure Selected Countries Costs',
    'configure_selected_regions_costs' => 'Configure Selected Regions Costs',
    'custom_costs_for_covered_areas' => 'Custom Costs for Covered Areas',
    'from' => 'From',
    'to' => 'To',
    'mile_cost' => 'Mile Cost',
    'shipping_cost' => 'Shipping Cost',
    'tax' => 'Tax',
    'insurance' => 'Insurance',
    'returned_mile_cost' => 'Returned Mile Cost',
    'returned_shipmen_cost' => 'Returned Shipment Cost',
    'loading_costs' => 'Loading Costs...',
    'here' => 'Here',
    'shipments' => 'Shipments',
    'shipment' => 'Shipment',
    'shipping_print' => 'Shipping Print',
    'Sender' => 'Sender',
    'City_collection' => 'City Collection',
    'City_of_origin' => 'City Of Origin',
    'Date_of_shipment' => 'Date Of Shipment',
    'Shipping_Time' => 'Shipping Time',
    'Contact_name' => 'Contact Name' ,
    'recipient' => 'Recipient',
    'delivery_city' => 'Delivery City',
    'Destination_city' => 'Destination City',
    'Shipping_quantity' => 'Shipping Quantity',
    'expected_date_of_arrival' => 'Expected Date Of Arrival',
    'contact_address' => 'Contact Address',
    'contact_name' => 'Contact Name' ,
    'shipping_record' => 'Shipping Record',
    'add_shipment' => 'Add shipment',
    'create_new_shipment' => 'Create new shipment',
    'edit_shipment' => 'Edit shipment',
    'shipment_list' => 'Shipments list',
    'selected_shipments' => 'selected shipments',
    'selected_missions' => 'selected missions',
    'import_shipments' => 'Import Shipments',
    'shipment_apis' => 'Shipment APIs',
    'barcode_scanner' => 'Barcode Scanner',
    'shipping_calculator' => 'Shipping Calculator',
    'all_Shipments' => 'All Shipments',
    'pending_shipments' =>'Pending Shipments',
    'delivered_shipments' => 'Delivered Shipments',
    'saved_shipments' => 'Saved Shipments',
    'in_progress_shipments' => 'In Progress Shipments',
    'saved' => 'Saved',
    'requested' => 'Requested',
    'approved' => 'Approved',
    'approve' => 'Approve',
    'closed' => 'Closed',
    'close' => 'Close',
    'assigned_approved' => 'Assigned & Approved',
    'recived' => 'Recived',
    'done' => 'Done',
    'transfer' => 'Transfer',
    'assigned' => 'Assigned',
    'received' => 'Received',
    'deliverd' => 'Deliverd',
    'supplied' => 'Supplied',
    'returned' => 'Returned',
    'returned_stock' => 'Returned Stock',
    'returned_deliverd' => 'Returned & Deliverd',
    'saved_pickup' => 'Saved Pickup',
    'saved_dropoff' => 'Saved Dropoff',
    'dropoff' => 'Dropoff',
    'pickup' => 'Pickup',
    'requested_pickup' => 'Requested Pickup',
    'please_configure_shipping_rates_in_creation_will_be_zero_without_configuration' => 'Please Configure Shipping rates in creation will be zero without configuration',
    'please_configure_your_covered_countries_and_regions' => 'Please Configure Your covered countries and region',
    'please_add_areas_before_creating_your_first_shipment' => 'Please Add areas before creating your first shipment',
    'please_add_package_types_before_creating_your_first_shipment' => 'Please Add package types before creating your first shipment',
    'please_add_branches_types_before_creating_your_first_shipment' => 'Please Add Branches types before creating your first shipment',
    'initiate_establishment_of_branch_first_before_proceeding_create_delivery_representative' => 'Initiate the establishment of a branch first before proceeding to create a delivery representative',
    'please_add_clients_types_before_creating_your_first_shipment' => 'Please Add Customers types before creating your first shipment',
    'please_add_receivers_types_before_creating_your_first_shipment' => 'Please Add Receivers types before creating your first shipment',
    'please_add_payments_before_creating_your_first_shipment' => 'Please Add Payments before creating your first shipment',
    'please_ask_your_administrator_to_configure_shipment_settings_first_before_you_can_create_a_new_shipment' => 'Please ask your administrator to configure shipment settings first, before you can create a new shipment!',
    'shipment_info' => 'Shipment Info',
    'shipment_type' => 'Shipment Type',
    'shipping_date' => 'Shipping Date',
    'collection_time' => 'Collection Time',
    'client_sender' => 'Customer/Sender',
    'choose_client' => 'Choose Customer',
    'choose_client_first' => 'Choose Customer First',
    'create_new_address' => 'Create New Address',
    'client_phone' => 'Customer Phone',
    'client_address' => 'Customer Address',
    'please_enter_all_reqired_fields' => 'Please Enter All Reqired Fields',
    'receiver_name' => 'Receiver Name',
    'receiver_phone' => 'Receiver Phone',
    'receiver_address' => 'Receiver Address',
    'receiver_list' => 'Receiver List',
    'create_new_receiver' => 'Create New Receiver',
    'edit_Receiver' => 'Edit Receiver',
    'receivers' => 'Receivers',
    'add_receiver' => 'Add Receiver',
    'receiver' => 'Receiver',
    'choose_address' => 'Choose Address',
    'no_addresses_found' => 'No Addresses Found',
    'address' => 'Address',
    'order_id' => 'Order ID',
    'attachments' => 'Attachments',
    'browse' => 'Browse',
    'choose_file' => 'Choose File',
    'package_info' => 'Package Info',
    'description' => 'Description',
    'quantity' => 'Quantity',
    'weight' => 'Weight',
    'dimensions_length_width_height' => 'Dimensions [Length x Width x Height] (cm)',
    'Weight_length_width_height_' => 'Weight x Length x Width x Height',
    'length' => 'Length',
    'width' => 'Width',
    'height' => 'Height',
    'delete' => 'Delete',
    'add' => 'Add',
    'amount_to_be_collected' => 'Amount to be Collected',
    'amount' => 'Amount',
    'due_date' => 'Due Date',
    'no_driver' => 'No Driver',
    'total_weight' => 'Total Weight',
    'calculated' => 'Calculated...',
    'location' => 'Location',
    'receiver_location' => 'Receiver Location',
    'please_select_package_type' => 'Please select package type',
    'estimation_cost' => 'Estimation Cost',
    'tax_duty' => 'Tax & Duty',
    'return_cost' => 'Return Cost',
    'return_mile_cost' => 'Return Mile Cost',
    'total_cost' => 'TOTAL COST',
    'status' => 'Status',
    'current_branch' => 'Current branch',
    'created_date' => 'Created date',
    'previous_branch' => 'Previous Branch',
    'max_delivery_days' => 'Max Delivery Days',
    'mission' => 'Mission',
    'ADDED_WHEN_SHIPMENT_CREATED' => 'ADDED WHEN SHIPMENT CREATED',
    'package_items' => 'Package Items',
    'qty' => 'Qty',
    'type' => 'Type',
    'value' => 'Value',
    'weigh_length_width_height' => 'Weight x Length x Width x Height',
    'PAYMENT_DATE' => 'PAYMENT DATE',
    'PAYMENT_STATUS' => 'PAYMENT STATUS',
    'PAYMENT_TYPE' => 'PAYMENT TYPE',
    'paid' => 'Paid',
    'pending' => 'Pending',
    'included_tax_insurance' => 'Included tax & insurance',
    'pay_now' => 'Pay Now',
    'copy_payment_link' => 'Copy Payment Link',
    'payment_link' => 'Payment Link',
    'print_label' => 'Print Label',
    'print_invoice' => 'Print Invoice',
    'shipment_return_reasons_log' => 'Shipment Return Reasons Log',
    'reason' => 'Reason',
    'shipment_status_log' => 'Shipment Status Log',
    'changed_from' => 'Changed from',
    'change' => 'Change',
    'change_status' => 'Change Status',
    'payment_link_copied' => 'Payment Link Copied',
    'KG' => 'KG',
    'CM' => 'CM',
    'approve_shipment_action' => 'Approve Shipment Action',
    'refuse_shipment_action' => 'Refuse Shipment Action',
    'create_pickup_mission' => 'Create Pickup Mission',
    'create_return_mission' => 'Create Return Mission',
    'create_supply_mission' => 'Create Supply Mission',
    'create_transfer_mission' => 'Create Transfer Mission',
    'create_delivery_mission' => 'Create Delivery Mission',
    'return_shipment_action' => 'Return Shipment Action',
    'assign_to_driver' => 'Assign to Driver',
    'requested_pickup' => 'Requested Pickup',
    'print_barcodes' => 'Print Barcodes',
    'transfer_to_branch' => 'Transfer To Branch',
    'to_returned_stock' => 'To Returned Stock',
    'return' => 'Return',
    'modal_message_sure' => 'Are you sure do this for',
    'send' => 'Send',
    'select_shipments_of_the_same_payment_method' => 'Select shipments of the same payment method',
    'please_select_shipments' => 'Please Select Shipments',
    'select_shipments_of_the_same_client_to_assign' => 'Select shipments of the same customer to Assign',
    'select_shipments_of_the_same_branch_to_transfer' => 'Select shipments of the same branch to Transfer',
    'this_shipment_already_in_mission' => 'This Shipment Already In Mission',
    'pickup_address' => 'Pickup Address',
    'delivery' => 'Delivery',
    'supply_address' => 'Supply Address',
    'supply' => 'Supply',
    'address' => 'Address',
    'create_mission' => 'Create Mission',
    'missions' => 'Missions',
    'all_missions' => 'All Missions',
    'pending_missions' => 'Pending Missions',
    'pickup_missions' => 'Pickup Missions',
    'delivery_missions' => 'Delivery Missions',
    'transfer_missions' => 'Transfer Missions',
    'supply_missions' => 'Supply Missions',
    'approve_assign_mission_action' => 'Approve & Assign Mission Action',
    'refuse_mission_action' => 'Refuse Mission Action',
    'confirm_mission_done_action' => 'Confirm Mission / Done Action',
    'confirm_mission_done' => 'Confirm Mission / Done',
    'receive_mission_action' => 'Receive Mission Action',
    'approve_assign' => 'Approve & Assign',
    'select_branch' => 'Select Branch',
    'please_select_branch_first' => 'Please Select Branch First',
    'select_driver' => 'Select Driver',
    'receive' => 'Receive',
    'please_confirm_the_signature' => 'Please Confirm The Signature',
    'please_enter_correct_OTP' => 'Please enter correct OTP',
    'please_enter_OTP_of-mission' => 'Please enter OTP of mission',
    'show' => 'Show',
    'draw_customer_signature' => 'Draw Customer Signature',
    'confirm_amount_and_done' => 'Confirm amount and Done',
    'from_branch' => 'From Branch',
    'to_branch' => 'To Branch',
    'notification_sms_not_sent_please_check_sms_verification' => 'Notification sms not sent please check sms verification',
    'MISSION_DETAILS' => 'MISSION DETAILS',
    'CREATED_DATE' => 'CREATED DATE',
    'CODE' => 'CODE',
    'MISSION_TYPE' => 'MISSION TYPE',
    'TRANSFER_TO_BRANCH' => 'TRANSFER TO BRANCH',
    'MISSION_ADDRESS' => 'MISSION ADDRESS',
    'MISSION_STATUS' => 'MISSION STATUS',
    'MISSION_Driver' => 'MISSION DRIVER',
    'DUE_DATE' =>'DUE DATE',
    'Print_Mission' => 'Print Mission',
    'TOTAL_COST' => 'TOTAL COST',
    'TOTAL_COD_AMOUNT' => 'TOTAL COD AMOUNT',
    'RETURN_AMOUNT' => 'RETURN AMOUNT',
    'mission_shipments' =>'Mission Shipments',
    'reschedule' => 'Reschedule',
    'COD_AMOUNT' => 'COD AMOUNT',
    'actions' => 'Actions',
    'check' => 'Check',
    'remove_from' => 'Remove From',
    'no_actions' => 'No Actions',
    'return_shipment' => 'Return Shipment',
    'remove' => 'Remove',
    'confirm_done' => 'Confirm Done',
    'transactions' => 'Transactions',
    'all_transactions' => 'All Transactions',
    'create_new_transaction' => 'Create new transaction',
    'transactions_list' => 'Transactions list',
    'add_transaction' => 'Add transaction',
    'beneficiary' => 'Beneficiary',
    'wallet_type' => 'Wallet Type',
    'add_to_wallet' => 'Add to wallet',
    'deduct_from_wallet' => 'Deduct from wallet',
    'please_select_branch_client_captain' => 'Please select branch , customer or driver',
    'captain_not_have_this_amount' => 'Driver Not Have This Amount',
    'pickup_delivery' => 'Pickup & Delivery',
    'created_by' => 'Created By',
    'manual' => 'Manual',
    'date' => 'Date',
    'payments_settings' => 'Payments Settings',
    'map_mode' => 'Map Mode',
    'map_mode_activation' => 'Map Mode Activation',
    'google_recaptcha' => 'Google reCAPTCHA',
    'map_setting' => 'Map Setting',
    'please_enter_google_map_key' => 'Please Enter Google Map Key',
    'please_enter_mapbox_key' => 'Please Enter Mapbox Key',
    'please_check_google_map_or_mapbox' => 'Please Check Google Map or Mapbox',
    'google_map' => 'Google Map',
    'mapbox' => 'Mapbox',
    'mapbox_KEY' => 'Mapbox KEY',
    'Google_Map_KEY' => 'Google Map KEY',
    'please_configure_your_map' => 'Please Configure Your Map',
    'google_reCAPTCHA_setting' => 'Google reCAPTCHA Setting',
    'site_KEY' => 'Site KEY',
    'HTTPS_Activation' => 'HTTPS Activation',
    'maintenance_mode' => 'Maintenance Mode',
    'maintenance_mode_activation' => 'Maintenance Mode Activation',
    'enabled' => 'Enabled',
    'disabled' => 'Disabled',
    'business_related' => 'Business Related',
    'email_verification' => 'Email Verification',
    'smtp_settings' => 'SMTP Settings',
    'an_email_has_been_sent' => 'An email has been sent.',
    'send_test_email' => 'Send test email',
    'Sendmail' => 'Sendmail',
    'SMTP' => 'SMTP',
    'Mailgun' => 'Mailgun',
    'MAIL_FROM_NAME' => 'MAIL FROM NAME',
    'MAIL_FROM_ADDRESS' => 'MAIL FROM ADDRESS',
    'website_mode_activation' => 'Website Mode Activation',
    'config_now' => 'Config Now',
    'you_need_to_configure_payment_correctly_to_enable_this_feature' => 'You need to configure Payment correctly to enable this feature',
    'paypal_payment_activation' => 'Paypal Payment Activation',
    'payStack_activation' => 'PayStack Activation',
    'sSlCommerz_activation' => 'SSlCommerz Activation',
    'instamojo_payment_activation' => 'Instamojo Payment Activation',
    'razor_pay_activation' => 'Razor Pay Activation',
    'stripe_payment_activation' => 'Stripe Payment Activation',
    'voguePay_activation' => 'VoguePay Activation',
    'payhere_activation' => 'Payhere Activation',
    'ngenius_activation' => 'Ngenius Activation',
    'iyzico_activation' => 'Iyzico Activation',
    'cash_payment_activation' => 'Cash Payment Activation',
    'invoice_payment_activation' => 'Invoice Payment Activation',
    'coming_soon' => 'Coming Soon',
    'payment_method' => 'Payment Method',
    'social_media_login' => 'Social Media Login',
    'facebook_login' => 'Facebook login',
    'you_need_to_configure_it_correctly_to_enable_this_feature' => 'You need to configure it correctly to enable this feature',
    'google_login' => 'Google login',
    'twitter_login' => 'Twitter login',
    'social_login' => 'Social Login',
    'SMS_Gateways' => 'SMS Gateways',
    'choose_default_SMS_Gatway' => 'Choose default SMS Gatway',
    'set_as_a_default_sms_gateway' => 'Set as a Default sms gateway',
    'import_shipments' => 'Import Shipments',
    'please_be_sure_shipments_have_right_branch' => 'Please: Be sure shipments have right branch, and make sure all required parameters in CSV',
    'shipment_CSV_Import' => 'Shipment CSV Import',
    'columns' => 'Columns',
    'parse_CSV' => 'Parse CSV',
    'CSV_file_to_import' => 'CSV file to import',
    'download_CSV' => 'Download CSV',
    'make_sure_all_required_parameters_in_CSV' => 'Make Sure All Required Parameters In CSV',
    'this_file_you_are_trying_to_import_is_not_the_file_that_you_should_upload' => 'This file you are trying to import is not the file that you should upload',
    'invalid_type' => 'Invalid Type',
    'invalid_branch' => 'Invalid Branch',
    'invalid_client_address' => 'Invalid Address',
    'invalid_client' => 'Invalid Customer',
    'invalid_country' => 'Invalid Country',
    'invalid_state' => 'Invalid Region',
    'invalid_area' => 'Invalid Area',
    'invalid_payment_method' => 'Invalid Payment Method',
    'invalid_payment_type' => 'Invalid Payment Type',
    'invalid_package' => 'Invalid Package',
    'invalid_delivery_time' => 'Invalid Delivery Time',
    'parameters' => 'Parameters',
    'details' => 'Details',
    'required' => 'Required',
    'click_to_get_iD' => 'Click To Get ID',
    'noting_found' => 'Noting found',
    'optional' => 'Optional',
    'default_is_your_phone' => 'Default is your phone',
    'your_addresses' => 'Your Addresses',
    'example' => 'Example',
    'default_is_1' => 'Default is 1',
    'default_is_0' => 'Default is 0',
    'click_to_get_value' => 'Click To Get Value',
    'view_all' => 'View All',
    'transacations'=> 'Transacations',
    'contains' => 'Contains',
    'cod' => 'COD',
    'shipment_code' => 'Shipment Code',
    'print_invoice' => 'Print Invoice',
    'INVOICE' => 'INVOICE',
    'DATE' => 'DATE',
    'SHIPMENT_CODE' => 'SHIPMENT CODE',
    'INVOICE_TO' => 'INVOICE TO',
    'package_items' => 'Package Items',
    'PAYMENT_TYPE' => 'PAYMENT TYPE',
    'PAYMENT_STATUS' => 'PAYMENT STATUS',
    'PAYMENT_DATE' => 'PAYMENT DATE',
    'TOTAL_COST' => 'TOTAL COST',
    'pending' => 'Pending',
    'currency_name' => 'Currency name',
    'currency_symbol' => 'Currency symbol',
    'currency_code' => 'Currency code',
    'exchange_rate' => 'Exchange Rate(1 USD = ?)',
    'symbol' => 'Symbol',
    'systemd_default_currency' => 'System Default Currency',
    'manifest' => 'Manifest',
    'manifest_missions' => 'Manifest Missions',
    'manifest_date' => 'Manifest Date',
    'get_ganifest' => 'Get Manifest',
    'MANIFEST_MISSIONS' => 'MANIFEST MISSIONS',
    'MANIFEST_DATE' => 'MANIFEST DATE',
    'DRIVER' => 'DRIVER',
    'print_manifest' => 'Print Manifest',
    'get_all_shipments' => 'Get All Shipments',
    'Endpoint' => 'Endpoint',
    'NOTE' => 'NOTE',
    'click_to_reGenerate_token' => 'Click To ReGenerate token',
    'copy' => 'Copy',
    'generating' => 'Generating...',
    'copied' => 'copied',
    'client_wallet_dashboard' => 'The amount you have on your wallet, Which you can request anytime',
    'your_wallet' => 'Your Wallet',
    'driver_wallet_dashboard' => 'The amount you have on your wallet, Which you should deliver to customer or company',
    'latest_shipments' => 'Latest Shipments',
    'drivers_wallet' => 'Drivers Wallet',
    'wallet' => 'Wallet',
    'drivers_custody' => 'Drivers custody',
    'current_manifest' => 'Current Manifest',
    'arrived' => 'Arrived',
    'active_missions' => 'Active Missions',
    'add_payment' => 'Add Payment',

    'table' => [
        '#'     => '#',
        'id'     => 'ID',
        'name'   => 'Name',
        'email'  => 'Email',
        'phone'  => 'Phone',
        'role'   => 'Role',
        'branch' => 'Branch',
        'hours'  => 'Hours',
        'region' => 'Region',
        'area'   => 'Area',
        'avatar' => 'Avatar',
        'cost'   => 'Cost',
        'code'   => 'Code',
        'type'   => 'Type',
        'full_name' => 'Full name',
        'owner_name' => 'Owner Name',
        'owner_phone' => 'Owner Phone',
        'password' => 'Password',
        'owner_national_id' => 'Owner National ID',
        'national_id' => 'National ID',
        'choose_branch' => 'Choose Branch',
        'choose_driver' => 'Choose Driver',
        'follow_up_mobile' => 'Follow Up Mobile',
        'follow_up_name' => 'Follow Up Name',
        'address' => 'address',
        'customer_source' => 'Customer Source',
        'owner_type' => 'Owner Type',
        'owner_name' => 'Owner Name',
    ],
    'missions_costs' => 'Missions Costs',
    'default_missions_costs' => 'Default Missions Costs',
    'custom_pickup_cost' => 'Custom Pickup Cost',
    'custom_supply_cost' => 'Custom Supply Cost',
    'default_costs_for_the_first_kg' => 'Default Costs For The First kg',
    'default_shipping_cost' => 'Default Shipping Cost',
    'shipping_cost' => 'Shipping Cost',
    'default_mile_cost' => 'Default Mile Cost',
    'mile_cost' => 'Mile Cost',
    'default_returned_mile_cost' => "Default Returned Mile Cost",
    'fixed_mile_cost_Kg' => 'Fixed Mile Cost/Kg',
    'fixed_returned_mile_cost_Kg' => 'Fixed Returned Mile Cost/Kg',
    'default_tax' => 'Default Tax',
    'default_insurance' => 'Default Insurance',
    'default_returned_shipment_cost' => 'Default Returned Shipment Cost',
    'extra_costs_for_kg' => 'Extra Costs For Kg',
    'fixed_shipping_cost_Kg' => 'Fixed Shipping Cost/Kg',
    'fixed_tax_Kg' => 'Fixed Tax/Kg',
    'fixed_insurance_Kg' => 'Fixed Insurance/Kg',
    'fixed_returned_shipment_cost_Kg' => 'Fixed Returned Shipment Cost/Kg',
    'extra_fees_for_package_types' => 'Extra Fees for Package Types',
    'extra_cost' => 'Extra Cost',
    'default_shippment_cost_by_miles_or_fees' => 'Default Shippment Cost By Miles Or Fees',
    'miles' => 'Miles',
    'fees' => 'Fees',
    'save_package_types_extra_fees' => 'Save Package Types Extra Fees',

    'currencies' => 'Currencies',
    'create_new_currency' => 'Create new currency',
    'add_Currency' => 'Add currency',
    'edit_currency' => 'Edit currency',
    'selected_currencies' => 'selected currencies',

    'staffs' => 'Staffs',
    'staff' => 'Staff',
    'all_staffs' => 'All Staffs',
    'staff_list' => 'Staff list',
    'create_new_staff' => 'Create new staff',
    'add_staff' => 'Add staff',
    'edit_staff' => 'Edit staff',
    'selected_staffs' => 'selected staff',

    'branches' => 'Branches',
    'all_branches' => 'All Branches',
    'branch_list' => 'Branch list',
    'create_new_branch' => 'Create new branch',
    'add_branch' => 'Add branch',
    'edit_branch' => 'Edit branch',
    'selected_branches' => 'selected branches',
    'more_info' => 'More info',

    'clients' => 'Customers',
    'all_clients' => 'All Customers',
    'client' => 'Customer',
    'client_list' => 'Customer list',
    'create_new_client' => 'Create new customer',
    'add_client' => 'Add customer',
    'edit_client' => 'Edit customer',
    'selected_clients' => 'selected customers',

    'add_new_address' => 'Add New Address',
    'drivers' => 'Drivers',
    'all_drivers' => 'All Drivers',
    'driver' => 'Driver',
    'driver_list' => 'Driver list',
    'create_new_driver' => 'Create new driver',
    'add_driver' => 'Add driver',
    'edit_driver' => 'Edit driver',
    'selected_drivers' => 'selected drivers',
    'shipment_notifications_settings' => 'Shipment Notifications Settings',
    'new_registeration' => 'New Registeration',
    'system_administrators' => 'System administrators',
    'users_roles' => 'Users roles',
    'users' => 'Users',
    'new_shipments' => 'New Shipments',
    'sender' => 'Sender',
    'assigned_driver' => 'Assigned Driver',
    'update_shipments' => 'Update Shipments',
    'new_driver' => 'New Driver',
    'new_customer' => 'New Customer',
    'new_staff' => 'New Staff',
    'new_mission' => 'New Mission',
    'mission_action' => 'Mission Action',
    'shipment_action' => 'Shipment Action',
    'aprroved_shipments' => 'Aprroved Shipments',
    'rejected_shipments' => 'Rejected Shipments',
    'assigned_shipments' => 'Assigned Shipments',
    'driver_received' => 'Driver Received',
    'delivered_shipments' => 'Delivered Shipments',
    'supplied_shipments' => 'Supplied Shipments',
    'request_returned_shipments' => 'Request Returned Shipments',
    'returned_to_stock_shipments' => 'Returned to stock Shipments',
    'returned_to_sender_shipments' => 'Returned to sender Shipments',
    'There_is_a_new_driver_created' => 'There is a new driver created',
    'There_is_a_new_shipment_created' => 'There is a new shipment created',
    'There_is_a_new_customer_created' => 'There is a new customer created',
    'There_is_approve_mission' => 'There is Approve mission',
    'There_is_assign_mission' => 'There is Assign & Approve mission',
    'There_is_update_shipment' => 'There is update shipment',
    'There_is_a_new_mission_created' => 'There is a new mission created',
    'There_is_an_updated_mission' => 'There is an updated mission',
    'There_is_an_updated_shipment' => 'There is an updated shipment',
    'payment_gateway' => 'Payment Gateway',
    'thank_you_for_paying_the_shipping_cost' => 'Thank you for paying the shipping cost',
    'back_to_dashboard' => 'Back To Dashboard',
    'back_to_shipment' => 'Back To Shipment',
    'to_receiver' =>'To Receiver',
    'barcode_scanner' => 'Barcode Scanner',
    'barcode' => 'Barcode',
    "cant_change_this_shipment" => "Can't Change This Shipment ",
    'no_shipment_with_this_barcode' => 'No Shipment With This Barcode ',
    'no_shipments_added' => 'No Shipments Added',
    'branch' => 'Branch' ,
    'captain' => 'Captain',
    'choose_captain' => 'Choose Captain ',
    'value' => 'Value',
    'choose_value' => 'Choose Value ',
    'PICKUP_TYPE' => 'Pickup',
    'DELIVERY_TYPE' => 'Delivery',
    'RETURN_TYPE' => 'Return',
    'SUPPLY_TYPE' => 'Supply',
    'TRANSFER_TYPE' => 'Transfer',
    'enter_your_tracking_code' => 'Enter your tracking code',
    'example_SH00001' => 'Example: SH00001',
    'search' => 'Search',

    'widget_tracking' => [
        'section_title' => 'Widget title',
        'section_bg' => 'Widget Background Color',
        'section_title_color' => 'Widget Title Color',
        'button_bg_color' => 'Button Background Color',
        'display_Widget_title' => 'Display Widget Title',
        'section_button_text_color' => 'Button Text Color',
        'view_style' => 'View style',
        'style_1' => 'Style 1',
        'style_2' => 'Style 2',
        'half_width' => 'Half Width',
    ],

    'tracking_note' => 'For inquiries about your shipments, please contact us from',
    'error_in_shipment_number'  => 'Please check the shipping number you entered',
    'tracking_shipment' => 'Tracking Shipment',
    'current_status' => 'Current Status',
    'created' => 'Created',

    'section_title' => 'Widget title',
    'widget_bg' => 'Widget Background Color',
    'widget_color' => 'Widget Color',
    'view_style' => 'View style',
    'style_1' => 'Style 1',
    'style_2' => 'Style 2',
    'display' => 'Display',
    'enter_ride_details' => 'Enter Ride Details',
    'personal' => 'Personal',
    'shipment_details' => 'Shipment Details',
    'shipping_price_may_vary_depending_on_weight' => 'Shipping Price May Vary Depending On Weight',
    'note_regarding_weight' => 'Note Regarding Weight',
    'distance' => 'Distance',
    'location' => 'Location',
    'ride_details' => 'Ride Details',
    'enter_valid_pickup_location' => 'Enter A Valid Pickup Location',
    'enter_location' => 'Enter A Location',
    'pickup_location' => 'Pickup location',
    'dropoff_location' => 'Drop-off Location',
    'enter_valid_dropoff_location' => 'Enter A Valid Drop-off Location',
    'details' => 'Details',
    'enter_valid_country' => 'Enter A Valid Country',
    'select_country' => 'Select Country',
    'select_country_first' => 'Select Country First',
    'select_region_first' => 'Select Region First',
    'package_details' => 'Package Details',
    'TOTAL_DISTANCE' => 'TOTAL DISTANCE',
    'TOTAL_TIME' => 'TOTAL TIME',
    'TOTAL_PRICE' => 'TOTAL PRICE',
    'SHIPPING_COST' => 'SHIPPING COST',
    'TAX_COST' => 'TAX COST',
    'INSURANCE_COST' => 'INSURANCE COST',
    'TOTAL_COST' => 'TOTAL COST',
    'Next_Step' => 'Next Step',
    'previous' => 'Previous',
    'client_sender_details' => 'Customer/Sender Details',
    'do_you_have_account' => 'Do You Have Account ?',
    'enter_a_valid_email' => 'Enter A Valid Email',
    'the_email_is_already_exist' => 'The Email Is Already Exist',
    'enter_a_email' => 'Enter A Email',
    'enter_a_password' => 'Enter A Password',
    'phone_number' => 'Phone Number',
    'receiver_details' => 'Receiver Details',
    'address_details' => 'Address Details',
    'client_location' => 'Customer Location',
    'calculating' => 'Calculating...',
    'register' => 'Register',
    'login' => 'Login',
    'already_have_an_account' => 'Already have an account ?',
    'tracking' => 'Tracking',
    'shipment_calculator' => 'Shipment Calculator',
    'error' => 'Error',
    'manage_address' => 'Manage Address',

    'create_a_new_account' => 'Create a New Account',
    'terms_and_conditions' => 'I agree with the Terms and Conditions',

    'reports' => 'Reports',
    'shipments_report' => 'Shipments Report',
    'missions_report' => 'Missions Report',
    'transactions_report' => 'Transactions Report',
    'branches_report' => 'Branches Report',
    'clients_report' => 'Customers Report',
    'drivers_report' => 'Drivers Report',
    'ready_for_shipping' => 'Ready for shipping',
    'in_Processing' => 'In Processing',
    'moving_to_branch' => 'Moving to Branch',
    'received_in_branch' => 'Received in Branch',
    'out_for_delivery' => 'Out For Delivery',
    'delivered' => 'Delivered',
    'returned_to_merchant' => 'Returned To Merchant',
    'please_enter_country_code' => 'Please Enter Country Code EX: +20',
    'payment_failed' => 'Payment Failed',
    'Invalid_phone_number' => 'Invalid Phone Number',
    'set_default_addresses' => 'Set Default Address',
    'send_app'=>'Send App',
    'check_number_length' => 'The number of characters must be more than 8',
    'support_codecanuon_1' => 'You Can Get Support Through CodeCanyon By ',
    'support_codecanuon_2' => 'If You Have Purchased 6 Months Of Support as a Customer  You Can Submit Support Ticket Or Simply Upgrade Your Support By Purchasing Another 6 Months, So That You Can Enjoy Instant Answers and Solutions.',
    'click_here' => 'Click Here',
    'send_email_support_1' => 'If You Have Any Questions, Please Do Not Hesitate To Contact Us Via Email At',
    'send_email_support_2' => 'and We Will Be Happy To Assist You. Please Note That We May Have Response Times Of Up To Two Business Days',
    'support_codecanuon_attention' => 'If you encounter any problem, you should create a ticket first by writing the full details of the problem you are facing, in addition to taking a screenshot of the error that appears on your screen. Our technical support team will provide you with the necessary assistance as soon as possible.',
    'support' => 'Support Team',
];