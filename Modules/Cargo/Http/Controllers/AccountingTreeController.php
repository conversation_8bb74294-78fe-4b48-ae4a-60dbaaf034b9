<?php

namespace Modules\Cargo\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\AccountingTreeDataTable;
use Modules\Cargo\Http\DataTables\ClientsDataTable;
use Modules\Cargo\Http\DataTables\ClientAddressDataTable;
use Modules\Cargo\Http\Requests\ClientRequest;
use Modules\Cargo\Http\Requests\ClientAddressRequest;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Shipment;
use App\Models\User;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use Modules\Users\Events\UserCreatedEvent;
use Modules\Users\Events\UserUpdatedEvent;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Http\Requests\AddressRequest;
use Modules\Cargo\Entities\BusinessSetting;
use app\Http\Helpers\ApiHelper;
use DB;
use Modules\Cargo\Events\AddClient;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Cargo\Entities\AccountingTree;

class AccountingTreeController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions
        $this->middleware('user_role:1|0|3')->only('index','clientsReport');
        $this->middleware('user_role:1|0|3|4')->only('show');
        $this->middleware('user_role:1|0|3')->only('create', 'store');
        $this->middleware('user_role:1|0|3')->only('edit');
        $this->middleware('user_role:1|0|3|4')->only('update');
        $this->middleware('user_role:1|0|3')->only('delete', 'multiDestroy');
        $this->middleware('user_role:4')->only('profile');
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(AccountingTreeDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Accounting')
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(AccountingTreeDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::'.$adminTheme.'.pages.accounting_tree.index', $share_data);
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('cargo::view.clients'),
                'path' => fr_route('accounting_tree.index')
            ],
            [
                'name' => __('Add Account'),
            ],
        ]);

        $query = AccountingTree::query();
        if ( !empty(request('parent_id')) ) {
          $AccountingTree =  AccountingTree::findOrFail(request('parent_id'));
            $query = $query->where('parent_id', $AccountingTree->parent_id ?? 0);
        }else{
            $query = $query->where('parent_id', 0);
        }
        $accounting_trees = $query->get();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::'.$adminTheme.'.pages.accounting_tree.create')->with(['accounting_trees' => $accounting_trees]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        $data = $request->only(['name','code','account_type','parent_id']);

        $data['created_by'] = auth()->check() ? auth()->id() : null;

        $parent = $request->parent_id ? AccountingTree::find($data['parent_id']) : null;

        $data['main_parent_id'] = $parent ? $parent->findMainParent()->id : null;

        $client = new AccountingTree();
        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }
   
        return redirect()->route('accounting_tree.index')->with(['message_alert' => __('cargo::messages.created')]);

    }


  

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Edit Account'),
            ],
        ]);

        $query = AccountingTree::query();
        if ( !empty(request('parent_id')) ) {
            $query = $query->where('parent_id', request('parent_id'));
        }else{
            $query = $query->where('parent_id', 0);
        }
        $accounting_trees = $query->get();
        
        $AccountingTree = AccountingTree::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.accounting_tree.edit')->with(['model' => $AccountingTree, 'accounting_trees' => $accounting_trees]);
    }



    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = AccountingTree::findOrFail($id);

        $data = $request->only(['name','code','account_type','parent_id']);

        $data['updated_by'] = auth()->check() ? auth()->id() : null;
   

        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }

        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = AccountingTree::findOrFail($id);
        $client->delete();
        return response()->json(['message' => __('cargo::messages.deleted')]);
    }



}