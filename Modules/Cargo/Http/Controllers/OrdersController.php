<?php

namespace Modules\Cargo\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\AccountingTreeDataTable;
use Modules\Cargo\Http\DataTables\ClientsDataTable;
use Modules\Cargo\Http\DataTables\ClientAddressDataTable;
use Modules\Cargo\Http\Requests\ClientRequest;
use Modules\Cargo\Http\Requests\ClientAddressRequest;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Shipment;
use App\Models\User;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use Modules\Users\Events\UserCreatedEvent;
use Modules\Users\Events\UserUpdatedEvent;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Http\Requests\AddressRequest;
use Modules\Cargo\Entities\BusinessSetting;
use app\Http\Helpers\ApiHelper;
use DB;
use Modules\Cargo\Events\AddClient;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Cargo\Entities\AccountingTree;
use Modules\Cargo\Entities\Flyer;
use Modules\Cargo\Entities\FlyerOrder;
use Modules\Cargo\Http\DataTables\OrdersDataTable;

class OrdersController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions
        // $this->middleware('user_role:1|0|3')->only('index','clientsReport');
        // $this->middleware('user_role:1|0|3|4')->only('show');
        // $this->middleware('user_role:1|0|3')->only('create', 'store');
        // $this->middleware('user_role:1|0|3')->only('edit');
        // $this->middleware('user_role:1|0|3|4')->only('update');
        // $this->middleware('user_role:1|0|3')->only('delete', 'multiDestroy');
        // $this->middleware('user_role:4')->only('profile');
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(OrdersDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Orders'),
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(OrdersDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::'.$adminTheme.'.pages.orders.index', $share_data);
    }


    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Orders'),
                'path' => fr_route('orders.index')
            ]
        ]);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::'.$adminTheme.'.pages.orders.create');
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        
        $client = Client::where('user_id', $user->id)->first();

        $flyer = Flyer::find($request->flyer_id);

        $price = $flyer->price * $request->qty;
        FlyerOrder::create([
            'client_id' => $client->id,
            'flyer_id' => $request->flyer_id,
            'qty' => $request->qty,
            'price' => $price,
            'size' => $request->size,
        ]);

        return redirect()->route('orders.index')->with(['message_alert' => __('cargo::messages.created')]);
    }



    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Edit Order'),
            ],
        ]);


        
        $AccountingTree = FlyerOrder::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.orders.edit')->with(['model' => $AccountingTree]);
    }


    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = FlyerOrder::findOrFail($id);

        $data = $request->only(['status']);   

        $client->fill($data);
        if (!$client->save()){
            throw new \Exception();
        }

        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = FlyerOrder::findOrFail($id);
        $client->delete();
        return response()->json(['message' => __('cargo::messages.deleted')]);
    }



}