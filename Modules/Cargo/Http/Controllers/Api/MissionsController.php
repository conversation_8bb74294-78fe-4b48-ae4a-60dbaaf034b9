<?php

namespace Modules\Cargo\Http\Controllers\Api;

use App\Http\Resources\MissionResource;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use App\Http\Resources\UserCollection;
use Modules\Cargo\Http\Controllers\MissionController;
use Modules\Cargo\Http\Controllers\ShipmentController;
use app\Http\Helpers\ApiHelper;
use App\Models\User;
use Modules\Cargo\Entities\Mission;
use Modules\Cargo\Entities\Shipment;
use Modules\Cargo\Entities\Driver;
use Modules\Cargo\Entities\Reason;
use Modules\Cargo\Entities\MissionLocation;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Entities\ShipmentMission;

class MissionsController extends Controller
{
    public function getCaptainMissions(Request $request)
    {
        
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if($user){

            $shipments= null;
            $driver = Driver::where('user_id',$user->id)->first();
            $missions = new Mission;
            $missions = $missions->where('captain_id', $driver->id);

            if($request->status_id){
                $missions = $missions->where('status_id', $request->status_id );
            }
            if($request->type_id){
                $missions = $missions->where('type', $request->type_id );
            }
            if($request->id){
                $missions = $missions->where('id', $request->id );
                $missions_ids = $missions->pluck('id')->toArray();
                $shipments_ids = ShipmentMission::whereIn('mission_id', $missions_ids)->pluck('shipment_id')->toArray();
                $shipments = Shipment::whereIn('id', $shipments_ids)->get();
            }

            $missions = $missions->get();


     
            return response()->json(
                [
                    'missions' => $missions,
                    'shipments' => $shipments 
                ]
            );
        }else{
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function getCaptainMissionsNew(Request $request)
    {
        
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if($user){

            $shipments= null;
            $driver = Driver::where('user_id',$user->id)->first();
            $missions = new Mission;
            $missions = $missions->where('captain_id', $driver->id);

            if($request->status_id){
                $missions = $missions->where('status_id', $request->status_id );
            }
            if($request->type_id){
                $missions = $missions->where('type', $request->type_id );
            }
            if($request->id){
                $missions = $missions->where('id', $request->id );
            }


            $missions = $missions->orderBy('priority', 'DESC')->orderBy('id', 'DESC')->get();


     
            return response()->json(
                [

                    'data' => MissionResource::collection($missions ) ,
                ]
            );
        }else{
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function changeMissionPriority(Request $request)
    {
        
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if($user){
            $request->validate([
                'id' => 'required',
                'priority' => 'required',
            ]);
            $mission = Mission::find($request->id);
            $mission->priority = $request->priority;
            $mission->save();
            return response()->json([ 'success' =>true , 'message' => 'Priority Changed Successfully!']);
        }else{
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function changeMissionApi(Request $request)
    {
        $apihelper = new ApiHelper();
        $missionsController = new MissionController(new AclRepository);
        $user = $apihelper->checkUser($request);
        if($user){
            $request->validate([
                'ids' => 'required',
                'to'  => 'required',
            ]);

            if ( $user->role == 5 ) {
                $driver = Driver::where('user_id',$user->id)->first();
                $missions_count = Mission::where('captain_id', $driver->id)->whereIn('id', $request->ids)->count();
            
                if( $missions_count != count($request->ids) ){ {
                    return response()->json([ 'success'=>false ,  'message' => 'Forbidden' ]);
                }

            
            }

            $status = $missionsController->change($request,$request->to,true);
            if($status){
                return response()->json(['success'=>true ,'message' => 'Status Changed Successfully!']);
            }else
                return response()->json($status);
            }
        }else{
            return response()->json(['success'=>false , 'message' => 'Not Authorized']);
        }
    }

    public function getMissionOfTheShipment(Request $request)
    {
        
            $request->validate([
                'shipment_code' => 'required',
            ]);
            $shipment = Shipment::where('code', $request->shipment_code)->first();

            if(!$shipment){
                return response()->json([ 'success' => false , 'message' => 'Shipment Not Found']);
            }


            $ShipmentMission = ShipmentMission::where('shipment_id', $shipment->id)
            ->orderBy('id', 'DESC')
            ->first();

            if(!$ShipmentMission){
                return response()->json([ 'success' => false , 'message' => 'Mission Not Found']);
            }

            $mission = Mission::find($ShipmentMission->mission_id);

            if(!$mission){
                return response()->json([ 'success' => false , 'message' => 'Mission Not Found']);
            }

          
            return response()->json([ 
             'success' => true ,
             'mission' => new MissionResource($mission)
            ]);

    }

    public function RemoveShipmetnFromMission(Request $request)
    {
        
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if($user){
            $request->validate([
                'mission_id'  => 'required',
                'shipment_id' => 'required',
                'reason'      => 'required',
            ]);

            $shipmentController = new ShipmentController(new AclRepository);
            $status = $shipmentController->removeShipmentFromMission($request, true);
            if($status){
                return response()->json(['message' => 'Shipment Removed Successfully']);
            }else{
                return response()->json($status);
            }
        }else{
            return response()->json(['message' => 'Not Authorized']);
        }
    }

    public function getReasons(Request $request)
    {
        
        $apihelper = new ApiHelper();
        $user = $apihelper->checkUser($request);
        if($user){
            $reasons = Reason::get();
            return response()->json($reasons);
        }else{
            return response()->json(['message' => 'Not Authorized']);
        }
    }
    
}