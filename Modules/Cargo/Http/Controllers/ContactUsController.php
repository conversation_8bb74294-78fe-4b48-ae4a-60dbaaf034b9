<?php

namespace Modules\Cargo\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\AccountingTreeDataTable;
use Modules\Cargo\Http\DataTables\ClientsDataTable;
use Modules\Cargo\Http\DataTables\ClientAddressDataTable;
use Modules\Cargo\Http\Requests\ClientRequest;
use Modules\Cargo\Http\Requests\ClientAddressRequest;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Shipment;
use App\Models\User;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use Modules\Users\Events\UserCreatedEvent;
use Modules\Users\Events\UserUpdatedEvent;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Http\Requests\AddressRequest;
use Modules\Cargo\Entities\BusinessSetting;
use app\Http\Helpers\ApiHelper;
use DB;
use Modules\Cargo\Events\AddClient;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Cargo\Entities\AccountingTree;
use Modules\Cargo\Entities\ContactUs;
use Modules\Cargo\Entities\FlyerOrder;
use Modules\Cargo\Http\DataTables\ContactUsDataTable;
use Modules\Cargo\Http\DataTables\OrdersDataTable;

class ContactUsController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions

    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(ContactUsDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Call Center'),
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(ContactUsDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::' . $adminTheme . '.pages.contact_us.index', $share_data);
    }




    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __(' Call Center'),
            ],
        ]);



        $AccountingTree = ContactUs::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.contact_us.edit')->with(['model' => $AccountingTree]);
    }

    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __(' '),
            ],

        ]);


        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.contact_us.create');
    }

    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */

    public function store(Request $request)
    {
       
        $request->validate([
            'title' => 'required',
            'message' => 'required',

        ]);

        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $user = auth()->user();


        $created_by = 'client';
        if ($user->role == 5) {
            $created_by = 'driver';
        } elseif ($user->role == 4) {
            $created_by = 'client';
        }

        ContactUs::create([
            'title' => $request->title,
            'message' => $request->message,
            'from' =>  $created_by,
            'creator_id' => $user->id,
        ]);


        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }

    public function update(Request $request, $id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = ContactUs::findOrFail($id);

        $data = $request->only(['status']);

        $client->fill($data);
        if (!$client->save()) {
            throw new \Exception();
        }

        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = ContactUs::findOrFail($id);
        $client->delete();
        return response()->json(['message' => __('cargo::messages.deleted')]);
    }
}
