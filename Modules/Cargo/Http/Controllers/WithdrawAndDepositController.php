<?php

namespace Modules\Cargo\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Http\DataTables\AccountingTreeDataTable;
use Modules\Cargo\Http\DataTables\ClientsDataTable;
use Modules\Cargo\Http\DataTables\ClientAddressDataTable;
use Modules\Cargo\Http\Requests\ClientRequest;
use Modules\Cargo\Http\Requests\ClientAddressRequest;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Shipment;
use App\Models\User;
use Modules\Cargo\Http\Helpers\UserRegistrationHelper;
use Modules\Users\Events\UserCreatedEvent;
use Modules\Users\Events\UserUpdatedEvent;
use Modules\Cargo\Entities\Package;
use Modules\Cargo\Entities\ClientPackage;
use Modules\Cargo\Entities\ClientAddress;
use Modules\Cargo\Http\Requests\AddressRequest;
use Modules\Cargo\Entities\BusinessSetting;
use app\Http\Helpers\ApiHelper;
use DB;
use Modules\Cargo\Events\AddClient;
use Modules\Acl\Repositories\AclRepository;
use Modules\Cargo\Http\Requests\RegisterRequest;
use Auth;
use Illuminate\Support\Facades\Validator;
use Modules\Cargo\Entities\AccountingTree;
use Modules\Cargo\Entities\WithdrawAndDeposit;
use Modules\Cargo\Http\DataTables\WithdrawAndDepositDataTable;

class WithdrawAndDepositController extends Controller
{
    private $aclRepo;

    public function __construct(AclRepository $aclRepository)
    {
        $this->aclRepo = $aclRepository;
        // check on permissions
        $this->middleware('user_role:1|0|3|4')->only('index', 'clientsReport');
        $this->middleware('user_role:1|0|3|4')->only('show');
        $this->middleware('user_role:1|0|3')->only('create', 'store');
        $this->middleware('user_role:1|0|3')->only('edit');
        $this->middleware('user_role:1|0|3|4')->only('update');
        $this->middleware('user_role:1|0|3')->only('delete', 'multiDestroy');
        $this->middleware('user_role:4')->only('profile');
    }

    public function getUsers(Request $request)
    {
        if ($request->for == 'driver') {
            $users = User::where('role', 5)->get();
        } elseif ($request->for == 'provider') {
            $users_ids = User::where('role', 4)->pluck('id')->toArray();

            $users= Client::whereIn('user_id', $users_ids)->get();


        } else {
            $users = User::all();
        }

        return response()->json($users);
    }

    public function getShipments(Request $request)
    {

        $client = Client::where('user_id', $request->user_id)->firstOrFail();

        $shipments = Shipment::where(function ($query) use ($client) {
            $query->where(function ($q) use ($client) {
                $q->where('client_id', $client->id)
                  ->where('is_withdraw', 0)
                  ->where('status_id', 7) // 7 is delivered
                  ->where('type', 1);
            })->orWhere(function ($q) use ($client) {
                $q->where('client_id', $client->id)
                  ->where('is_withdraw', 0)
                  ->whereIn('status_id', [11]) // 10 is supplied as return from reciever and 11 is return to provider
                  ->where('type', 3);
            })->orWhere(function ($q) use ($client) {
                $q->where('client_id', $client->id)
                  ->where('is_withdraw', 0)
                  ->where('status_id', 11) // 11 is return to provider
                  ->where('type', 1);
            });
        })->get();


        return response()->json($shipments);
    }

    /**
     * Display a listing of the resource.
     * @return Renderable
     */
    public function index(WithdrawAndDepositDataTable $dataTable)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Withdraw And Deposit')
            ]
        ]);
        $data_with = [];
        $share_data = array_merge(get_class_vars(WithdrawAndDepositDataTable::class), $data_with);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return $dataTable->render('cargo::' . $adminTheme . '.pages.withdraw_and_deposit.index', $share_data);
    }

    public function trial_balance()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __(' ميزان المراجعة ')
            ]
        ]);

        $plus = WithdrawAndDeposit::where('type', '+')->sum('amount');
        $minus = WithdrawAndDeposit::where('type', '-')->sum('amount');
        $total = $plus - $minus;

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.withdraw_and_deposit.trial_balance', compact('total', 'plus', 'minus'));
    }

    /**
     * Show the form for creating a new resource.
     * @return Renderable
     */
    public function create()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Withdraw And Deposit'),
                'path' => fr_route('withdraw_and_deposit.index')
            ],
            [
                'name' => __('Add Withdraw And Deposit'),
            ],
        ]);

        $accounting_trees =  AccountingTree::all();

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.withdraw_and_deposit.create')->with(['accounting_trees' => $accounting_trees]);
    }

    /**
     * Store a newly created resource in storage.
     * @param Request $request
     * @return Renderable
     */
    public function store(Request $request)
    {
        $data = $request->only(['code', 'type', 'for', 'accounting_tree_id', 'to_user_id', 'date_time', 'amount', 'desc', 'shipment_ids']);


        if (empty($data['shipment_ids'])) {
            $data['shipment_ids'] = json_encode([]);
        }
        $data['created_by'] = auth()->check() ? auth()->id() : null;

        $client = new WithdrawAndDeposit();
        $client->fill($data);
        if (!$client->save()) {
            throw new \Exception();
        }

        // Fix: Handle JSON decoding safely
        $shipmentIds = [];
        if (!empty($data['shipment_ids'])) {
            $decodedIds = json_decode($data['shipment_ids'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($decodedIds)) {
                $shipmentIds = $decodedIds;
            }
        }

        if (!empty($shipmentIds)) {
            Shipment::whereIn('id', $shipmentIds)->update(['is_withdraw' => 1]);
        }

        return redirect()->route('withdraw_and_deposit.index')->with(['message_alert' => __('cargo::messages.created')]);
    }



    public function show($id)
    {
        try {
            breadcrumb([
                [
                    'name' => __('cargo::view.dashboard'),
                    'path' => fr_route('admin.dashboard')
                ],
                [
                    'name' => __('Withdraw And Deposit')
                ],
            ]);

            $WithdrawAndDeposit = WithdrawAndDeposit::findOrFail($id);

            // Fix: Handle null, empty, or invalid JSON in shipment_ids
            $shipmentIds = [];
            if (!empty($WithdrawAndDeposit->shipment_ids)) {
                $decodedIds = json_decode($WithdrawAndDeposit->shipment_ids, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decodedIds)) {
                    $shipmentIds = $decodedIds;
                }
            }

            // Get shipments only if we have valid IDs
            $shipments = collect();
            if (!empty($shipmentIds)) {
                $shipments = Shipment::whereIn('id', $shipmentIds)->get();
            }

            $adminTheme = env('ADMIN_THEME', 'adminLte');
            return view('cargo::' . $adminTheme . '.pages.withdraw_and_deposit.show')->with(['model' => $WithdrawAndDeposit, 'shipments' => $shipments]);

        } catch (\Exception $e) {
            // Log the error for debugging
            \Log::error('WithdrawAndDeposit show error for ID ' . $id . ': ' . $e->getMessage());

            // Return a user-friendly error page or redirect
            return back()->with(['error_message_alert' => 'Error loading withdraw and deposit record: ' . $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified resource.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('Edit Withdraw And Deposit'),
            ],
        ]);

        $accounting_trees =  AccountingTree::all();

        $AccountingTree = AccountingTree::findOrFail($id);
        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.withdraw_and_deposit.edit')->with(['model' => $AccountingTree, 'accounting_trees' => $accounting_trees]);
    }



    /**
     * Update the specified resource in storage.
     * @param Request $request
     * @param int $id
     * @return Renderable
     */
    public function update(Request $request, $id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = WithdrawAndDeposit::findOrFail($id);

        $data = $request->only(['code', 'type', 'for', 'accounting_tree_id', 'to_user_id', 'date_time', 'amount', 'desc', 'shipment_ids']);

        $data['updated_by'] = auth()->check() ? auth()->id() : null;


        $client->fill($data);
        if (!$client->save()) {
            throw new \Exception();
        }

        return redirect()->back()->with(['message_alert' => __('cargo::messages.saved')]);
    }


    /**
     * Remove the specified resource from storage.
     * @param int $id
     * @return Renderable
     */
    public function destroy($id)
    {
        if (env('DEMO_MODE') == 'On') {
            return redirect()->back()->with(['error_message_alert' => __('view.demo_mode')]);
        }

        $client = WithdrawAndDeposit::findOrFail($id);
        $client->delete();
        return response()->json(['message' => __('cargo::messages.deleted')]);
    }
}
