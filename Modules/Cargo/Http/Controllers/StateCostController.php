<?php

namespace Modules\Cargo\Http\Controllers;

use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Cargo\Entities\State;
use Modules\Cargo\Entities\ShipmentSetting;

class StateCostController extends Controller
{
    /**
     * Display a listing of states with their costs.
     * @return Renderable
     */
    public function index()
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('State Costs Management')
            ],
        ]);

        $states = State::with('country')->where('covered', 1)->paginate(20);

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.state_costs.index', compact('states'));
    }

    /**
     * Show the form for editing the specified state costs.
     * @param int $id
     * @return Renderable
     */
    public function edit($id)
    {
        breadcrumb([
            [
                'name' => __('cargo::view.dashboard'),
                'path' => fr_route('admin.dashboard')
            ],
            [
                'name' => __('State Costs Management'),
                'path' => route('admin.state-costs.index')
            ],
            [
                'name' => __('Edit State Costs')
            ],
        ]);

        $state = State::findOrFail($id);
        $is_def_mile_or_fees = ShipmentSetting::getVal('is_def_mile_or_fees') ?? 2;

        $adminTheme = env('ADMIN_THEME', 'adminLte');
        return view('cargo::' . $adminTheme . '.pages.state_costs.form', compact('state', 'is_def_mile_or_fees'));
    }

    /**
     * Update the specified state costs in storage.
     * @param Request $request
     * @param int $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'def_shipping_cost' => 'nullable|numeric|min:0',
            'def_return_cost' => 'nullable|numeric|min:0',
            'def_shipping_cost_gram' => 'nullable|numeric|min:0',
            'def_return_cost_gram' => 'nullable|numeric|min:0',
        ]);

        $state = State::findOrFail($id);

        // Update cost fields
        $state->update([
            'def_shipping_cost' => $request->def_shipping_cost,
            'def_return_cost' => $request->def_return_cost,
            'def_shipping_cost_gram' => $request->def_shipping_cost_gram ?? 0,
            'def_return_cost_gram' => $request->def_return_cost_gram ?? 0,
        ]);

        return redirect()->route('admin.state-costs.index')
            ->with(['message_alert' => __('State costs updated successfully')]);
    }

    /**
     * Copy costs from one state to another
     * @param Request $request
     * @return Response
     */
    public function copyCosts(Request $request)
    {
        $request->validate([
            'from_state_id' => 'required|exists:states,id',
            'to_state_ids' => 'required|array',
            'to_state_ids.*' => 'exists:states,id',
        ]);

        $fromState = State::findOrFail($request->from_state_id);
        $toStates = State::whereIn('id', $request->to_state_ids)->get();

        $costFields = [
            'def_shipping_cost', 'def_return_cost', 'def_shipping_cost_gram', 'def_return_cost_gram'
        ];

        foreach ($toStates as $toState) {
            $updateData = [];
            foreach ($costFields as $field) {
                $updateData[$field] = $fromState->$field;
            }
            $toState->update($updateData);
        }

        return redirect()->route('admin.state-costs.index')
            ->with(['message_alert' => __('Costs copied successfully to selected states')]);
    }

    /**
     * Get state costs via AJAX for client form display
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function ajaxGetStateCosts(Request $request)
    {
        try {
            $stateId = $request->get('state_id');

            if (!$stateId) {
                return response()->json([
                    'success' => false,
                    'message' => 'State ID is required'
                ]);
            }

            $state = State::find($stateId);

            if (!$state) {
                return response()->json([
                    'success' => false,
                    'message' => 'State not found'
                ]);
            }

            // Check if costs are configured
            $hasCosts = $state->def_shipping_cost || $state->def_return_cost ||
                       $state->def_shipping_cost_gram || $state->def_return_cost_gram;

            if (!$hasCosts) {
                return response()->json([
                    'success' => false,
                    'message' => 'No costs configured for this state'
                ]);
            }

            return response()->json([
                'success' => true,
                'costs' => [
                    'def_shipping_cost' => $state->def_shipping_cost ?: 0,
                    'def_return_cost' => $state->def_return_cost ?: 0,
                    'def_shipping_cost_gram' => $state->def_shipping_cost_gram ?: 0,
                    'def_return_cost_gram' => $state->def_return_cost_gram ?: 0,
                ],
                'state_name' => $state->name
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving state costs: ' . $e->getMessage()
            ]);
        }
    }
}
