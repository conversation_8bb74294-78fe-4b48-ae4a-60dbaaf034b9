<?php

namespace Modules\Cargo\Http\Filter;


/**
 * Use this class to add filter on users in query database.
 */
class WithdrawAndDepositFilter
{

    /**
     * user query.
     * @var object
     */
    public $query;

    /**
     * request data.
     * @var object
     */
    public $request;


    public function __construct($query, $request)
    {
        $this->query = $query;
        $this->request = $request;

        return $this;
    }

    /**
     * request data.
     * @param array $key_filters {
     * @item string
     * }
     * @return query
     */
    public function filterBy(...$key_filters)
    {
        $filter = $this->request->filter;
        $query = $this->query;


        $user = auth()->user();

        if($user->role == 4){
            $query->where('to_user_id', $user->id );

        }


        if ($filter) {
            $filter_array = is_array($key_filters[0]) ? $key_filters[0] : $key_filters;
           // dd($filter_array , $filter);
            foreach ($filter_array as $key) {
                // check on created_at | filter table



            // branch_id
            if ($key == 'branch_id') {
                if (isset($filter['branch_id']) && $filter['branch_id'] != '') {
                    $query->where('branch_id', $filter['branch_id']);
                }
            }
            // user_id
            if ($key == 'to_user_id') {
                if (isset($filter['to_user_id']) && $filter['to_user_id'] != '') {
                    $query->where('to_user_id', $filter['to_user_id']);
                }
            }

            if ($key == 'accounting_tree_id') {
                if (isset($filter['accounting_tree_id']) && $filter['accounting_tree_id'] != '') {
                    $query->where('accounting_tree_id', $filter['accounting_tree_id']);
                }
            }
            if ($key == 'type') {
                if (isset($filter['type']) && $filter['type'] != '') {
                    $query->where('type', $filter['type']);
                }
            }
            if ($key == 'for') {
                if (isset($filter['for']) && $filter['for'] != '') {
                    $query->where('for', $filter['for']);
                }
            }



                require app_path('Helpers/globalFilter/created_at.php');
            }
        }

        return $query;
    }
}