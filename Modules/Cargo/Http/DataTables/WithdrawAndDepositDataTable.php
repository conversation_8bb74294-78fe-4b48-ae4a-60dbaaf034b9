<?php

namespace Modules\Cargo\Http\DataTables;

use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Yajra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;
use Yajra\DataTables\Html\Button;
use Modules\Cargo\Entities\Client;
use Illuminate\Http\Request;
use Mo<PERSON>les\Cargo\Entities\WithdrawAndDeposit;
use Modules\Cargo\Http\Filter\ClientFilter;
use Modules\Cargo\Http\Filter\WithdrawAndDepositFilter;

class WithdrawAndDepositDataTable extends DataTable
{

    public $table_id = 'clients';
    public $btn_exports = [
        'excel',
        'print',
        'pdf'
    ];
    public $filters = ['branch_id' , 'to_user_id','accounting_tree_id', 'created_at' , 'type' , 'for'];
    /**
     * Build DataTable class.
     *
     * @param  mixed  $query  Results from query() method.
     *
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
    
    
        return datatables()
            ->eloquent($query)
            ->rawColumns(['action', 'select','name'])

            ->filterColumn('user', function($query, $keyword) {
                $query->where('name', 'LIKE', "%$keyword%");
                $query->orWhere('email', 'LIKE', "%$keyword%");
                $query->orWhere('responsible_mobile', 'LIKE', "%$keyword%");
            })

            ->orderColumn('name', function ($query, $order) {
                $query->orderBy('name', $order);
            })

            ->addColumn('select', function (WithdrawAndDeposit $model) {
                $adminTheme = env('ADMIN_THEME', 'adminLte');
                return view($adminTheme.'.components.modules.datatable.columns.checkbox', ['model' => $model, 'ifHide' => $model->id == 0]);
            })
            ->editColumn('type', function (WithdrawAndDeposit $model) {
                return $model->type == '+' ? 'سند قبض' : 'سند دفع' ;
            })
            ->editColumn('created_at', function (WithdrawAndDeposit $model) {
                return date('d M, Y H:i', strtotime($model->created_at));
            })
            ->editColumn('accounting_tree_id', function (WithdrawAndDeposit $model) {
                return $model->accounting_tree?->name ?? ''; 
            })

            
            // ->editColumn('avatar', function (Staff $model) {
            //     return '
            //     <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
            //         <a href="' . fr_route('users.show', ['id' => $model->id]) . '">
            //             <div class="symbol-label">
            //                 <img src="' . $model->avatarImage . '" class="w-100">
            //             </div>
            //         </a>
            //     </div>';
            // })
  
            ->addColumn('action', function (WithdrawAndDeposit $model) {
                $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.withdraw_and_deposit.columns.actions', ['model' => $model, 'table_id' => $this->table_id]);
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param  Client  $model
     *
     * @return Client
     */
    public function query(WithdrawAndDeposit $model, Request $request)
    {
        $query = $model->newQuery();

        // class filter for user only
        $client_filter = new WithdrawAndDepositFilter($query, $request);

        $query = $client_filter->filterBy($this->filters);

        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        $lang = \LaravelLocalization::getCurrentLocale();
        $lang = get_locale_name_by_code($lang, $lang);

        return $this->builder()
            ->setTableId($this->table_id)
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->stateSave(true)
            ->orderBy(1)
            ->responsive()
            ->autoWidth(false)
            ->parameters([
                'scrollX' => true,
                'dom' => 'Bfrtip',
                'bDestroy' => true,
                'language' => ['url' => "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/$lang.json"],
                'buttons' => [
                    ...$this->buttonsExport(),
                ],
            ])
            ->addTableClass('align-middle table-row-dashed fs-6 gy-5');
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {

        $columns = [];
        $columns[] =  Column::computed('select')
        ->title('
            <div class="form-check form-check-sm form-check-custom form-check-solid">
                <input class="form-check-input checkbox-all-rows" type="checkbox">
            </div>
        ')
        ->responsivePriority(-1)
        ->addClass('not-export')
        ->width(50) ;
        $columns[] = Column::make('id')->title(__('cargo::view.table.#'))->width(50);
       // $columns[] = Column::make('code')->title(__('code'));
        $columns[] = Column::make('amount')->title(__('amount'));

        $columns[] = Column::make('type')->title(__('type'));

        if(auth()->user()->role != 4 ) {
        $columns[] = Column::make('for')->title(__('for'));
        $columns[] = Column::make('accounting_tree_id')->title(__('accounting_tree'));
        }
      
        $columns[] = Column::make('created_at')->title(__('view.created_at'));
        $columns[] = Column::computed('action')
            ->title(__('view.action'))
            ->addClass('text-center not-export')
            ->responsivePriority(-1);
        return $columns;

      
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'clients_'.date('YmdHis');
    }


    /**
     * Transformer buttons export.
     *
     * @return string
     */
    protected function buttonsExport()
    {
        $btns = [];
        foreach($this->btn_exports as $btn) {
            $btns[] = [
                'extend' => $btn,
                'exportOptions' => [
                    'columns' => 'th:not(.not-export)'
                ]
            ];
        }
        return $btns;
    }
}
