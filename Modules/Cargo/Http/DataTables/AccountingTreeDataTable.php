<?php

namespace Modules\Cargo\Http\DataTables;

use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Ya<PERSON>ra\DataTables\Html\Column;
use Yajra\DataTables\Services\DataTable;
use Ya<PERSON>ra\DataTables\Html\Button;
use Modules\Cargo\Entities\Client;
use Illuminate\Http\Request;
use Mo<PERSON>les\Cargo\Entities\AccountingTree;
use Modules\Cargo\Http\Filter\ClientFilter;

class AccountingTreeDataTable extends DataTable
{

    public $table_id = 'clients';
    public $btn_exports = [
        'excel',
        'print',
        'pdf'
    ];
    public $filters = ['branch_id','name', 'created_at' , 'status'];
    /**
     * Build DataTable class.
     *
     * @param  mixed  $query  Results from query() method.
     *
     * @return \Yajra\DataTables\DataTableAbstract
     */
    public function dataTable($query)
    {
        if ( !empty(request('parent_id')) ) {
            $query = $query->where('parent_id', request('parent_id'));
        }else{
            $query = $query->where('parent_id', 0)->orWhereNull('parent_id');
        }
       
    
        return datatables()
            ->eloquent($query)
            ->rawColumns(['action', 'select','name'])

            ->filterColumn('user', function($query, $keyword) {
                $query->where('name', 'LIKE', "%$keyword%");
                $query->orWhere('email', 'LIKE', "%$keyword%");
                $query->orWhere('responsible_mobile', 'LIKE', "%$keyword%");
            })

            ->orderColumn('name', function ($query, $order) {
                $query->orderBy('name', $order);
            })

            ->addColumn('select', function (AccountingTree $model) {
                $adminTheme = env('ADMIN_THEME', 'adminLte');
                return view($adminTheme.'.components.modules.datatable.columns.checkbox', ['model' => $model, 'ifHide' => $model->id == 0]);
            })
            ->editColumn('account_type', function (AccountingTree $model) {
                return $model->account_type == '+' ? 'مدين' : 'دائن';
            })
            ->editColumn('created_at', function (AccountingTree $model) {
                return date('d M, Y H:i', strtotime($model->created_at));
            })

            ->addColumn('all_total_amount', function (AccountingTree $model) {
 
               $subAccountingTreeIds = $model->getAllSubAccountingTrees()->pluck('id')->toArray();
               array_push($subAccountingTreeIds, $model->id);

                $total_positive = \Modules\Cargo\Entities\WithdrawAndDeposit::whereIn('accounting_tree_id', $subAccountingTreeIds)
                ->where('type', '+')
                ->sum('amount');

                $total_negative = \Modules\Cargo\Entities\WithdrawAndDeposit::whereIn('accounting_tree_id', $subAccountingTreeIds)
                ->where('type', '-')
                ->sum('amount');

                return $total_positive - $total_negative;

            })

            // ->editColumn('avatar', function (Staff $model) {
            //     return '
            //     <div class="symbol symbol-circle symbol-40px overflow-hidden me-3">
            //         <a href="' . fr_route('users.show', ['id' => $model->id]) . '">
            //             <div class="symbol-label">
            //                 <img src="' . $model->avatarImage . '" class="w-100">
            //             </div>
            //         </a>
            //     </div>';
            // })
  
            ->addColumn('action', function (AccountingTree $model) {
                $adminTheme = env('ADMIN_THEME', 'adminLte');return view('cargo::'.$adminTheme.'.pages.accounting_tree.columns.actions', ['model' => $model, 'table_id' => $this->table_id]);
            });
    }

    /**
     * Get query source of dataTable.
     *
     * @param  Client  $model
     *
     * @return Client
     */
    public function query(AccountingTree $model, Request $request)
    {
        $query = $model->newQuery();

        // class filter for user only
        $client_filter = new ClientFilter($query, $request);

        $query = $client_filter->filterBy($this->filters);

        return $query;
    }

    /**
     * Optional method if you want to use html builder.
     *
     * @return \Yajra\DataTables\Html\Builder
     */
    public function html()
    {
        $lang = \LaravelLocalization::getCurrentLocale();
        $lang = get_locale_name_by_code($lang, $lang);

        return $this->builder()
            ->setTableId($this->table_id)
            ->columns($this->getColumns())
            ->minifiedAjax()
            ->stateSave(true)
            ->orderBy(1)
            ->responsive()
            ->autoWidth(false)
            ->parameters([
                'scrollX' => true,
                'dom' => 'Bfrtip',
                'bDestroy' => true,
                'language' => ['url' => "//cdn.datatables.net/plug-ins/9dcbecd42ad/i18n/$lang.json"],
                'buttons' => [
                    ...$this->buttonsExport(),
                ],
            ])
            ->addTableClass('align-middle table-row-dashed fs-6 gy-5');
    }

    /**
     * Get columns.
     *
     * @return array
     */
    protected function getColumns()
    {
        return [
            Column::computed('select')
                    ->title('
                        <div class="form-check form-check-sm form-check-custom form-check-solid">
                            <input class="form-check-input checkbox-all-rows" type="checkbox">
                        </div>
                    ')
                    ->responsivePriority(-1)
                    ->addClass('not-export')
                    ->width(50),
            Column::make('id')->title(__('cargo::view.table.#'))->width(50),
            Column::make('code')->title(__('code')),
            Column::make('name')->title(__('name')),
            Column::make('account_type')->title(__('account_type')),
            Column::make('all_total_amount')->title(__('all_total_amount')),
            Column::make('created_at')->title(__('view.created_at')),
            Column::computed('action')
                ->title(__('view.action'))
                ->addClass('text-center not-export')
                ->responsivePriority(-1)
        ];
    }

    /**
     * Get filename for export.
     *
     * @return string
     */
    protected function filename()
    {
        return 'clients_'.date('YmdHis');
    }


    /**
     * Transformer buttons export.
     *
     * @return string
     */
    protected function buttonsExport()
    {
        $btns = [];
        foreach($this->btn_exports as $btn) {
            $btns[] = [
                'extend' => $btn,
                'exportOptions' => [
                    'columns' => 'th:not(.not-export)'
                ]
            ];
        }
        return $btns;
    }
}
