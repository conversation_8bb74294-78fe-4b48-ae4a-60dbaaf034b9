<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class AddCostFieldsToStatesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('states', function (Blueprint $table) {
            // Only the 4 essential cost fields
            $table->double('def_shipping_cost')->nullable()->after('covered');
            $table->double('def_return_cost')->nullable()->after('def_shipping_cost');
            $table->double('def_shipping_cost_gram')->default(0)->after('def_return_cost');
            $table->double('def_return_cost_gram')->default(0)->after('def_shipping_cost_gram');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('states', function (Blueprint $table) {
            $table->dropColumn([
                'def_shipping_cost',
                'def_return_cost',
                'def_shipping_cost_gram',
                'def_return_cost_gram'
            ]);
        });
    }
}
