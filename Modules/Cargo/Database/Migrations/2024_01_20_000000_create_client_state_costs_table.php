<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateClientStateCostsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('client_state_costs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('client_id');
            $table->unsignedBigInteger('state_id');
            $table->double('def_shipping_cost')->nullable();
            $table->double('def_return_cost')->nullable();
            $table->double('def_shipping_cost_gram')->default(0);
            $table->double('def_return_cost_gram')->default(0);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('client_id')->references('id')->on('clients')->onDelete('cascade');
            $table->foreign('state_id')->references('id')->on('states')->onDelete('cascade');
            
            // Unique constraint to prevent duplicate client-state combinations
            $table->unique(['client_id', 'state_id']);
            
            // Indexes for better performance
            $table->index(['client_id']);
            $table->index(['state_id']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('client_state_costs');
    }
}
