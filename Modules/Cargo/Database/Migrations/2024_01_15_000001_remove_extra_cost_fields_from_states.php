<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class RemoveExtraCostFieldsFromStates extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('states', function (Blueprint $table) {
            // Remove extra fields that are not needed
            $table->dropColumn([
                'def_mile_cost',
                'def_tax',
                'def_insurance',
                'def_return_mile_cost',
                'def_mile_cost_gram',
                'def_tax_gram',
                'def_insurance_gram',
                'def_return_mile_cost_gram',
                'pickup_cost',
                'supply_cost'
            ]);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('states', function (Blueprint $table) {
            // Add back the removed fields
            $table->double('def_mile_cost')->nullable()->after('def_return_cost_gram');
            $table->double('def_tax')->nullable()->after('def_mile_cost');
            $table->double('def_insurance')->nullable()->after('def_tax');
            $table->double('def_return_mile_cost')->nullable()->after('def_insurance');
            $table->double('def_mile_cost_gram')->default(0)->after('def_return_mile_cost');
            $table->double('def_tax_gram')->default(0)->after('def_mile_cost_gram');
            $table->double('def_insurance_gram')->default(0)->after('def_tax_gram');
            $table->double('def_return_mile_cost_gram')->default(0)->after('def_insurance_gram');
            $table->double('pickup_cost')->default(0)->after('def_return_mile_cost_gram');
            $table->double('supply_cost')->default(0)->after('pickup_cost');
        });
    }
}
