<?php

namespace Modules\Cargo\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ClientStateCost extends Model
{
    use HasFactory;

    protected $fillable = [
        'client_id',
        'state_id',
        'def_shipping_cost',
        'def_return_cost',
        'def_shipping_cost_gram',
        'def_return_cost_gram'
    ];

    protected $table = 'client_state_costs';

    /**
     * Get the client that owns this cost configuration
     */
    public function client()
    {
        return $this->belongsTo(Client::class, 'client_id');
    }

    /**
     * Get the state for this cost configuration
     */
    public function state()
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Get client-specific costs for a state, fallback to default state costs
     * @param int $clientId
     * @param int $stateId
     * @return array
     */
    public static function getClientStateCosts($clientId, $stateId)
    {
        // First try to get client-specific costs
        $clientCosts = self::where('client_id', $clientId)
                          ->where('state_id', $stateId)
                          ->first();

        if ($clientCosts) {
            return [
                'def_shipping_cost' => $clientCosts->def_shipping_cost,
                'def_return_cost' => $clientCosts->def_return_cost,
                'def_shipping_cost_gram' => $clientCosts->def_shipping_cost_gram,
                'def_return_cost_gram' => $clientCosts->def_return_cost_gram,
                'source' => 'client_specific'
            ];
        }

        // Fallback to default state costs
        $state = State::find($stateId);
        if ($state) {
            return [
                'def_shipping_cost' => $state->def_shipping_cost,
                'def_return_cost' => $state->def_return_cost,
                'def_shipping_cost_gram' => $state->def_shipping_cost_gram,
                'def_return_cost_gram' => $state->def_return_cost_gram,
                'source' => 'default_state'
            ];
        }

        // No costs found
        return [
            'def_shipping_cost' => 0,
            'def_return_cost' => 0,
            'def_shipping_cost_gram' => 0,
            'def_return_cost_gram' => 0,
            'source' => 'none'
        ];
    }

    /**
     * Set or update client-specific costs for a state
     * @param int $clientId
     * @param int $stateId
     * @param array $costs
     * @return ClientStateCost
     */
    public static function setClientStateCosts($clientId, $stateId, $costs)
    {
        return self::updateOrCreate(
            [
                'client_id' => $clientId,
                'state_id' => $stateId
            ],
            [
                'def_shipping_cost' => $costs['def_shipping_cost'] ?? 0,
                'def_return_cost' => $costs['def_return_cost'] ?? 0,
                'def_shipping_cost_gram' => $costs['def_shipping_cost_gram'] ?? 0,
                'def_return_cost_gram' => $costs['def_return_cost_gram'] ?? 0
            ]
        );
    }

    /**
     * Delete client-specific costs for a state (revert to default)
     * @param int $clientId
     * @param int $stateId
     * @return bool
     */
    public static function deleteClientStateCosts($clientId, $stateId)
    {
        return self::where('client_id', $clientId)
                  ->where('state_id', $stateId)
                  ->delete();
    }
}
