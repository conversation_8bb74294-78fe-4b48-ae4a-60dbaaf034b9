<?php

namespace Modules\Cargo\Entities;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Facades\DB;
use Modules\Cargo\Entities\Client;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Staff;
use Modules\Cargo\Entities\Mission;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ShipmentChat extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'shipment_chat';


    protected static function boot()
    {
        parent::boot();


        static::created(function ($chat) {
            $token_array = [];
        
            try {
                $shipment = Shipment::find($chat->shipment_id);
                if (!$shipment) {
                    throw new \Exception("Shipment not found for ID: {$chat->shipment_id}");
                }
        
                $client_id = $shipment->client_id;

                $client_user = User::where('id', Client::find($client_id)->user_id )->first();
                
                $shipment_mission = ShipmentMission::where('shipment_id', $shipment->id)
                    ->orderByDesc('id')->first();
                
                $mission = $shipment_mission ? Mission::where('id',$shipment_mission->mission_id)
                ->whereIn('status_id' , [5,2,1] )->first() : null;
                

                $driver_user = $mission ? User::where('id', Driver::where('id', $mission->captain_id)->value('user_id'))->first() : null;
                
                $receiver = Receiver::where('receiver_mobile', 'like', '%' . $shipment->reciver_phone . '%')->first();
        
                // Assign FCM tokens based on who created the chat
                switch ($chat->created_by) {
                    case 'receiver':
                        if ($client_user?->fcm_token) $token_array[] = $client_user->fcm_token;
                        if ($driver_user?->fcm_token) $token_array[] = $driver_user->fcm_token;
                        break;
        
                    case 'admin':
                        if ($client_user?->fcm_token) $token_array[] = $client_user->fcm_token;
                        if ($driver_user?->fcm_token) $token_array[] = $driver_user->fcm_token;
                        if ($receiver?->fcm_token) $token_array[] = $receiver->fcm_token;
                        break;
        
                    case 'driver':
                        if ($client_user?->fcm_token) $token_array[] = $client_user->fcm_token;
                        if ($receiver?->fcm_token) $token_array[] = $receiver->fcm_token;
                        break;
        
                    case 'client':
                        if ($driver_user?->fcm_token) $token_array[] = $driver_user->fcm_token;
                        if ($receiver?->fcm_token) $token_array[] = $receiver->fcm_token;
                        break;
                }
        
                if (!empty($token_array)) {
                    $data = [
                        'type' => 'chat',
                        'id'   => (string) $shipment->id,
                    ];
                    $message = "You have a new message in #{$shipment->id} shipment chat";
                    
                    pushNotification($token_array, $message, $data);
                }
        
            } catch (\Throwable $th) {
                DB::table('errors')->insert([
                    'error'      => $th->getMessage() . " (shipment chat)",
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
            }
        });
        

        // static::created(function ($chat) {

        //     $token_array = [];

        //     try {

        //         $shipment = Shipment::where('id', $chat->shipment_id)->first();
        //         $client = Client::where('id', $shipment->client_id)->first();
        //         $client_user = User::where('id', $client->user_id)->first();


        //         $shipment_mission = ShipmentMission::where('shipment_id',  $shipment->id)
        //             ->orderBy('id', 'desc')->first();

        //         $mission = Mission::where('id',  $shipment_mission->mission_id)->first();


        //         $driver = Driver::where('id', $mission->captain_id)->first();
        //         $driver_user = User::where('id', $driver->user_id)->first();

        //         $reciver = Receiver::where('receiver_mobile', 'like', '%' .  $shipment->reciver_phone . '%')->first();


        //         if ($chat->created_by == 'receiver') {
        //             array_push( $token_array  , $client_user->fcm_token ?? ' '  ) ;
        //             array_push( $token_array  , $driver_user->fcm_token ?? ' '  ) ;
        //         }elseif ( $chat->created_by == 'admin' ) {
        //             array_push( $token_array  , $client_user->fcm_token ?? ' '  ) ;
        //             array_push( $token_array  , $driver_user->fcm_token ?? ' '  ) ;
        //             array_push( $token_array  , $reciver->fcm_token ?? ' '  ) ;
        //         }elseif ( $chat->created_by == 'driver' ) {
        //             array_push( $token_array  , $client_user->fcm_token ?? ' '  ) ;
        //             array_push( $token_array  , $reciver->fcm_token ?? ' '  ) ;
        //         }elseif ( $chat->created_by == 'client' ) {
        //             array_push( $token_array  , $driver_user->fcm_token ?? ' '  ) ;
        //             array_push( $token_array  , $reciver->fcm_token ?? ' '  ) ;
        //         }

        //         $data = [
        //             'type' => 'chat',
        //             'id' => (string)  $shipment->id 
        //         ];
        //         $message = "you have new message in #$shipment->id  shipment chat ";

        //         pushNotification(  $token_array , $message  , $data  ) ;


        //     } catch (\Throwable $th) {

        //         DB::table('errors')->insert([
        //             'error' => $th->getMessage() ? $th->getMessage() . " shipment chat" : "shipment chat",
        //             'created_at' => now(), // If your table has timestamps
        //             'updated_at' => now(),
        //         ]);
                
        //     }

        // });






        // static::updated(function ($shipment) {
        //     // Get old and new status values
        //     $oldStatus = $shipment->getOriginal('status_id');
        //     $newStatus = $shipment->status_id;

        //     // Check if the status_id has changed
        //     if ($oldStatus !== $newStatus) {
        //         // Log::info('Shipment Status Updated', [
        //         //     'shipment_id' => $shipment->id,
        //         //     'old_status'  => $oldStatus,
        //         //     'new_status'  => $newStatus
        //         // ]);

               
        //     }
        // });
  














    }


    public function userName()
    {

        if (in_array($this->created_by, ['admin', 'driver', 'client'])) {
            return User::find($this->created_by_id)?->name .  "($this->created_by)"   ?? "user not found" .  "($this->created_by)";
        } elseif ($this->created_by == 'receiver') {
            return Receiver::find($this->created_by_id)?->name . "($this->created_by)" ?? "user not found" .  "($this->created_by)";
        }
        return "user not found";
    }
}
