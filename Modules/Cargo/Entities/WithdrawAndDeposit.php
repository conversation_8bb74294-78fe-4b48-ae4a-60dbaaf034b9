<?php

namespace Modules\Cargo\Entities;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Staff;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class WithdrawAndDeposit extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'withdraw_and_deposit';


    public function accounting_tree()
    {
        return $this->belongsTo(AccountingTree::class, 'accounting_tree_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'to_user_id');
    }
    
    
}
