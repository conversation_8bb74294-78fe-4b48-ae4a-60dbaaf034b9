<?php

namespace Modules\Cargo\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class FlyerOrder extends Model
{
    use HasFactory;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'flyers_orders';


    public function flyer(){
        return $this->belongsTo('Modules\Cargo\Entities\Flyer',  'flyer_id');
    }

    public function client(){
        return $this->belongsTo('Modules\Cargo\Entities\Client',  'client_id');
    }
    

 
}
