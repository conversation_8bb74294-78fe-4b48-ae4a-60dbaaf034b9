<?php

namespace Modules\Cargo\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Modules\Cargo\Entities\Branch;
use Modules\Cargo\Entities\Staff;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class AccountingTree extends Model implements HasMedia
{
    use HasFactory;
    use InteractsWithMedia;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'accounting_tree';




    public function parent()
    {
        return $this->belongsTo(AccountingTree::class, 'parent_id');
    }

    public function findMainParent()
    {
        $AccountingTree = $this;
        while ($AccountingTree->parent) {
            $AccountingTree = $AccountingTree->parent; 
        }
        return $AccountingTree; 
    }


    public function children()
    {
        return $this->hasMany(AccountingTree::class, 'parent_id');
    }


    public function getAllSubAccountingTrees()
    {
        $allChildren = collect(); // Initialize an empty collection
    
        foreach ($this->children as $child) {
            $allChildren->push($child); // Add the direct child
            $allChildren = $allChildren->merge($child->getAllSubAccountingTrees()); // Merge recursively
        }
    
        return $allChildren;
    }
    
    
}
