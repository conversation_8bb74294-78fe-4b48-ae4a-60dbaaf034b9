<?php

namespace Modules\Cargo\Entities;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class ContactUs extends Model
{
    use HasFactory;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'contact_us';


    // public function userName()
    // {

    //     if (in_array($this->from, ['admin', 'driver', 'client'])) {
    //         return User::find($this->creator_id)?->name .  "($this->from)"   ?? "user not found" .  "($this->from)";
    //     } elseif ($this->from == 'receiver') {
    //         return Receiver::find($this->creator_id)?->name . "($this->from)" ?? "user not found" .  "($this->from)";
    //     }
    //     return "user not found";
    // }

    public function userName()
    {
        if (in_array($this->from, ['admin', 'driver', 'client'])) {
            $user = User::find($this->creator_id);
            $name = $user?->name . " ($this->from)" ?? "user not found ($this->from)";
            $url = $this->from == 'client'  ? route('clients.show',  $this->creator_id) : ''; // Adjust this route
        } elseif ($this->from == 'receiver') {
            $user = Receiver::find($this->creator_id);
            $name = $user?->name . " ($this->from)" ?? "user not found ($this->from)";
            $url = route('receiver.edit',  $this->creator_id); // Adjust this route
        } else {
            return "user not found";
        }

        return "<a href='$url'>$name</a>";
    }

    public function userNameClear()
    {
        if (in_array($this->from, ['admin', 'driver', 'client'])) {
            $user = User::find($this->creator_id);
            $name = $user?->name . " ($this->from)" ?? "user not found ($this->from)";
            $url = $this->from == 'client'  ? route('clients.show',  $this->creator_id) : ''; // Adjust this route
        } elseif ($this->from == 'receiver') {
            $user = Receiver::find($this->creator_id);
            $name = $user?->name . " ($this->from)" ?? "user not found ($this->from)";
            $url = route('receiver.edit',  $this->creator_id); // Adjust this route
        } else {
            return "user not found";
        }

        return $name ;
    }



}
