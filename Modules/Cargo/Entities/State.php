<?php

namespace Modules\Cargo\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class State extends Model
{
    use HasFactory;

    protected $fillable = [];
    protected $guarded = [];
    protected $table = 'states';

    protected static function newFactory()
    {
        return \Modules\Cargo\Database\factories\StateFactory::new();
    }

    /**
     * Get the country that owns the state
     */
    public function country()
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the areas for the state
     */
    public function areas()
    {
        return $this->hasMany(Area::class, 'state_id');
    }

    /**
     * Get the clients in this state
     */
    public function clients()
    {
        return $this->hasMany(Client::class, 'state_id');
    }
}
