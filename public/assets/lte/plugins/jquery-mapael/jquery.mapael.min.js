!function(a){"object"==typeof exports?module.exports=a(require("jquery"),require("raphael"),require("jquery-mousewheel")):"function"==typeof define&&define.amd?define(["jquery","raphael","mousewheel"],a):a(j<PERSON><PERSON><PERSON>,<PERSON>,jQuery.fn.mousewheel)}(function(a,b,c,d){"use strict";var e=function(b,c){var d=this;d.container=b,d.$container=a(b),d.options=d.extendDefaultOptions(c),d.zoomTO=0,d.zoomCenterX=0,d.zoomCenterY=0,d.previousPinchDist=0,d.zoomData={zoomLevel:0,zoomX:0,zoomY:0,panX:0,panY:0},d.currentViewBox={x:0,y:0,w:0,h:0},d.panning=!1,d.zoomAnimID=null,d.zoomAnimStartTime=null,d.zoomAnimCVBTarget=null,d.$map=a("."+d.options.map.cssClass,d.container),d.initialMapHTMLContent=d.$map.html(),d.$tooltip={},d.paper={},d.areas={},d.plots={},d.links={},d.legends={},d.mapConf={},d.customEventHandlers={},d.init()};return e.prototype={MouseOverFilteringTO:120,panningFilteringTO:150,panningEndFilteringTO:50,zoomFilteringTO:150,resizeFilteringTO:150,init:function(){var c=this;if(""===c.options.map.cssClass||0===a("."+c.options.map.cssClass,c.container).length)throw new Error("The map class `"+c.options.map.cssClass+"` doesn't exists");if(c.$tooltip=a("<div>").addClass(c.options.map.tooltip.cssClass).css("display","none"),c.$map.empty().append(c.$tooltip),a.mapael&&a.mapael.maps&&a.mapael.maps[c.options.map.name])c.mapConf=a.mapael.maps[c.options.map.name];else{if(!(a.fn.mapael&&a.fn.mapael.maps&&a.fn.mapael.maps[c.options.map.name]))throw new Error("Unknown map '"+c.options.map.name+"'");c.mapConf=a.fn.mapael.maps[c.options.map.name],window.console&&window.console.warn&&window.console.warn("Extending $.fn.mapael is deprecated (map '"+c.options.map.name+"')")}if(c.paper=new b(c.$map[0],c.mapConf.width,c.mapConf.height),!0===c.isRaphaelBBoxBugPresent())throw c.destroy(),new Error("Can't get boundary box for text (is your container hidden? See #135)");c.$container.addClass("mapael"),c.options.map.tooltip.css&&c.$tooltip.css(c.options.map.tooltip.css),c.setViewBox(0,0,c.mapConf.width,c.mapConf.height),c.options.map.width?c.paper.setSize(c.options.map.width,c.mapConf.height*(c.options.map.width/c.mapConf.width)):c.initResponsiveSize(),a.each(c.mapConf.elems,function(a){c.areas[a]={},c.areas[a].options=c.getElemOptions(c.options.map.defaultArea,c.options.areas[a]?c.options.areas[a]:{},c.options.legend.area),c.areas[a].mapElem=c.paper.path(c.mapConf.elems[a])}),c.options.map.beforeInit&&c.options.map.beforeInit(c.$container,c.paper,c.options),a.each(c.mapConf.elems,function(a){c.initElem(a,"area",c.areas[a])}),c.links=c.drawLinksCollection(c.options.links),a.each(c.options.plots,function(a){c.plots[a]=c.drawPlot(a)}),c.$container.on("zoom.mapael",function(a,b){c.onZoomEvent(a,b)}),c.options.map.zoom.enabled&&c.initZoom(c.mapConf.width,c.mapConf.height,c.options.map.zoom),c.options.map.zoom.init!==d&&(c.options.map.zoom.init.animDuration===d&&(c.options.map.zoom.init.animDuration=0),c.$container.trigger("zoom",c.options.map.zoom.init)),c.createLegends("area",c.areas,1),c.createLegends("plot",c.plots,c.paper.width/c.mapConf.width),c.$container.on("update.mapael",function(a,b){c.onUpdateEvent(a,b)}),c.$container.on("showElementsInRange.mapael",function(a,b){c.onShowElementsInRange(a,b)}),c.initDelegatedMapEvents(),c.initDelegatedCustomEvents(),c.options.map.afterInit&&c.options.map.afterInit(c.$container,c.paper,c.areas,c.plots,c.options),a(c.paper.desc).append(" and Mapael "+c.version+" (https://www.vincentbroute.fr/mapael/)")},destroy:function(){var b=this;b.$container.off(".mapael"),b.$map.off(".mapael"),b.onResizeEvent&&a(window).off("resize.mapael",b.onResizeEvent),b.$map.empty(),b.$map.html(b.initialMapHTMLContent),a.each(b.legends,function(c){a.each(b.legends[c],function(a){var d=b.legends[c][a];d.container.empty(),d.container.html(d.initialHTMLContent)})}),b.$container.removeClass("mapael"),b.$container.removeData("mapael"),b.container=d,b.$container=d,b.options=d,b.paper=d,b.$map=d,b.$tooltip=d,b.mapConf=d,b.areas=d,b.plots=d,b.links=d,b.customEventHandlers=d},initResponsiveSize:function(){var b=this,c=null,d=function(a){var c=b.$map.width();if(b.paper.width!==c){var d=c/b.mapConf.width;b.paper.setSize(c,b.mapConf.height*d),!0!==a&&b.options.legend.redrawOnResize&&b.createLegends("plot",b.plots,d)}};b.onResizeEvent=function(){clearTimeout(c),c=setTimeout(function(){d()},b.resizeFilteringTO)},a(window).on("resize.mapael",b.onResizeEvent),d(!0)},extendDefaultOptions:function(b){return b=a.extend(!0,{},e.prototype.defaultOptions,b),a.each(["area","plot"],function(c,d){if(a.isArray(b.legend[d]))for(var f=0;f<b.legend[d].length;++f)b.legend[d][f]=a.extend(!0,{},e.prototype.legendDefaultOptions[d],b.legend[d][f]);else b.legend[d]=a.extend(!0,{},e.prototype.legendDefaultOptions[d],b.legend[d])}),b},initDelegatedMapEvents:function(){var b,c=this,e={area:c.areas,"area-text":c.areas,plot:c.plots,"plot-text":c.plots,link:c.links,"link-text":c.links};c.$container.on("mouseover.mapael","[data-id]",function(){var f=this;clearTimeout(b),b=setTimeout(function(){var b=a(f),g=b.attr("data-id"),h=b.attr("data-type");if(e[h]!==d)c.elemEnter(e[h][g]);else if("legend-elem"===h||"legend-label"===h){var i=b.attr("data-legend-id"),j=b.attr("data-legend-type");c.elemEnter(c.legends[j][i].elems[g])}},c.MouseOverFilteringTO)});var f;c.$container.on("mousemove.mapael","[data-id]",function(b){var g=this;clearTimeout(f),f=setTimeout(function(){var f=a(g),h=f.attr("data-id"),i=f.attr("data-type");e[i]!==d&&c.elemHover(e[i][h],b)},0)}),c.$container.on("mouseout.mapael","[data-id]",function(){var g=this;clearTimeout(b),clearTimeout(f);var h=a(g),i=h.attr("data-id"),j=h.attr("data-type");if(e[j]!==d)c.elemOut(e[j][i]);else if("legend-elem"===j||"legend-label"===j){var k=h.attr("data-legend-id"),l=h.attr("data-legend-type");c.elemOut(c.legends[l][k].elems[i])}}),c.$container.on("click.mapael","[data-id]",function(b,f){var g=a(this),h=g.attr("data-id"),i=g.attr("data-type");if(e[i]!==d)c.elemClick(e[i][h]);else if("legend-elem"===i||"legend-label"===i){var j=g.attr("data-legend-id"),k=g.attr("data-legend-type");c.handleClickOnLegendElem(c.legends[k][j].elems[h],h,j,k,f)}})},initDelegatedCustomEvents:function(){var b=this;a.each(b.customEventHandlers,function(c){var e=c+".mapael.custom";b.$container.off(e).on(e,"[data-id]",function(e){var f=a(this),g=f.attr("data-id"),h=f.attr("data-type").replace("-text","");if(!b.panning&&b.customEventHandlers[c][h]!==d&&b.customEventHandlers[c][h][g]!==d){var i=b.customEventHandlers[c][h][g];i.options.eventHandlers[c](e,g,i.mapElem,i.textElem,i.options)}})})},initElem:function(b,c,e){var f=this,g=a(e.mapElem.node);if(e.options.href&&(e.options.attrs.cursor="pointer",e.options.text&&(e.options.text.attrs.cursor="pointer")),e.mapElem.attr(e.options.attrs),g.attr({"data-id":b,"data-type":c}),e.options.cssClass!==d&&g.addClass(e.options.cssClass),e.options.text&&e.options.text.content!==d){var h=f.getTextPosition(e.mapElem.getBBox(),e.options.text.position,e.options.text.margin);e.options.text.attrs.text=e.options.text.content,e.options.text.attrs.x=h.x,e.options.text.attrs.y=h.y,e.options.text.attrs["text-anchor"]=h.textAnchor,e.textElem=f.paper.text(h.x,h.y,e.options.text.content),e.textElem.attr(e.options.text.attrs),a(e.textElem.node).attr({"data-id":b,"data-type":c+"-text"})}e.options.eventHandlers&&f.setEventHandlers(b,c,e),f.setHoverOptions(e.mapElem,e.options.attrs,e.options.attrsHover),e.textElem&&f.setHoverOptions(e.textElem,e.options.text.attrs,e.options.text.attrsHover)},initZoom:function(b,c,e){var f=this,g=!1,h=0,i=0,j={reset:function(){f.$container.trigger("zoom",{level:0})},in:function(){f.$container.trigger("zoom",{level:"+1"})},out:function(){f.$container.trigger("zoom",{level:-1})}};a.extend(f.zoomData,{zoomLevel:0,panX:0,panY:0}),a.each(e.buttons,function(b,c){if(j[b]===d)throw new Error("Unknown zoom button '"+b+"'");var e=a("<div>").addClass(c.cssClass).html(c.content).attr("title",c.title);e.on("click.mapael",j[b]),f.$map.append(e)}),f.options.map.zoom.mousewheel&&f.$map.on("mousewheel.mapael",function(a){var b=a.deltaY>0?1:-1,c=f.mapPagePositionToXY(a.pageX,a.pageY);f.$container.trigger("zoom",{fixedCenter:!0,level:f.zoomData.zoomLevel+b,x:c.x,y:c.y}),a.preventDefault()}),f.options.map.zoom.touch&&(f.$map.on("touchstart.mapael",function(a){2===a.originalEvent.touches.length&&(f.zoomCenterX=(a.originalEvent.touches[0].pageX+a.originalEvent.touches[1].pageX)/2,f.zoomCenterY=(a.originalEvent.touches[0].pageY+a.originalEvent.touches[1].pageY)/2,f.previousPinchDist=Math.sqrt(Math.pow(a.originalEvent.touches[1].pageX-a.originalEvent.touches[0].pageX,2)+Math.pow(a.originalEvent.touches[1].pageY-a.originalEvent.touches[0].pageY,2)))}),f.$map.on("touchmove.mapael",function(a){var b=0,c=0;if(2===a.originalEvent.touches.length){if(b=Math.sqrt(Math.pow(a.originalEvent.touches[1].pageX-a.originalEvent.touches[0].pageX,2)+Math.pow(a.originalEvent.touches[1].pageY-a.originalEvent.touches[0].pageY,2)),Math.abs(b-f.previousPinchDist)>15){var d=f.mapPagePositionToXY(f.zoomCenterX,f.zoomCenterY);c=(b-f.previousPinchDist)/Math.abs(b-f.previousPinchDist),f.$container.trigger("zoom",{fixedCenter:!0,level:f.zoomData.zoomLevel+c,x:d.x,y:d.y}),f.previousPinchDist=b}return!1}})),f.$map.on("dragstart",function(){return!1});var k=null,l=null;a("body").on("mouseup.mapael"+(e.touch?" touchend.mapael":""),function(){g=!1,clearTimeout(k),clearTimeout(l),k=setTimeout(function(){f.panning=!1},f.panningEndFilteringTO)}),f.$map.on("mousedown.mapael"+(e.touch?" touchstart.mapael":""),function(a){clearTimeout(k),clearTimeout(l),a.pageX!==d?(g=!0,h=a.pageX,i=a.pageY):1===a.originalEvent.touches.length&&(g=!0,h=a.originalEvent.touches[0].pageX,i=a.originalEvent.touches[0].pageY)}).on("mousemove.mapael"+(e.touch?" touchmove.mapael":""),function(j){var m=f.zoomData.zoomLevel,n=0,o=0;if(clearTimeout(k),clearTimeout(l),j.pageX!==d?(n=j.pageX,o=j.pageY):1===j.originalEvent.touches.length?(n=j.originalEvent.touches[0].pageX,o=j.originalEvent.touches[0].pageY):g=!1,g&&0!==m){var p=(h-n)/(1+m*e.step)*(b/f.paper.width),q=(i-o)/(1+m*e.step)*(c/f.paper.height),r=Math.min(Math.max(0,f.currentViewBox.x+p),b-f.currentViewBox.w),s=Math.min(Math.max(0,f.currentViewBox.y+q),c-f.currentViewBox.h);return(Math.abs(p)>5||Math.abs(q)>5)&&(a.extend(f.zoomData,{panX:r,panY:s,zoomX:r+f.currentViewBox.w/2,zoomY:s+f.currentViewBox.h/2}),f.setViewBox(r,s,f.currentViewBox.w,f.currentViewBox.h),l=setTimeout(function(){f.$map.trigger("afterPanning",{x1:r,y1:s,x2:r+f.currentViewBox.w,y2:s+f.currentViewBox.h})},f.panningFilteringTO),h=n,i=o,f.panning=!0),!1}})},mapPagePositionToXY:function(a,b){var c=this,d=c.$map.offset(),e=c.options.map.width?c.mapConf.width/c.options.map.width:c.mapConf.width/c.$map.width(),f=1/(1+c.zoomData.zoomLevel*c.options.map.zoom.step);return{x:f*e*(a-d.left)+c.zoomData.panX,y:f*e*(b-d.top)+c.zoomData.panY}},onZoomEvent:function(b,c){var e,f,g,h,i,j=this,k=j.zoomData.zoomLevel,l=1+j.zoomData.zoomLevel*j.options.map.zoom.step,m=c.animDuration!==d?c.animDuration:j.options.map.zoom.animDuration;if(c.area!==d){if(j.areas[c.area]===d)throw new Error("Unknown area '"+c.area+"'");var n=c.areaMargin!==d?c.areaMargin:10,o=j.areas[c.area].mapElem.getBBox(),p=o.width+2*n,q=o.height+2*n;c.x=o.cx,c.y=o.cy,k=Math.min(Math.floor((j.mapConf.width/p-1)/j.options.map.zoom.step),Math.floor((j.mapConf.height/q-1)/j.options.map.zoom.step))}else if(c.level!==d&&(k="string"==typeof c.level?"+"===c.level.slice(0,1)||"-"===c.level.slice(0,1)?j.zoomData.zoomLevel+parseInt(c.level,10):parseInt(c.level,10):c.level<0?j.zoomData.zoomLevel+c.level:c.level),c.plot!==d){if(j.plots[c.plot]===d)throw new Error("Unknown plot '"+c.plot+"'");c.x=j.plots[c.plot].coords.x,c.y=j.plots[c.plot].coords.y}else{if(c.latitude!==d&&c.longitude!==d){var r=j.mapConf.getCoords(c.latitude,c.longitude);c.x=r.x,c.y=r.y}c.x===d&&(c.x=j.currentViewBox.x+j.currentViewBox.w/2),c.y===d&&(c.y=j.currentViewBox.y+j.currentViewBox.h/2)}k=Math.min(Math.max(k,j.options.map.zoom.minLevel),j.options.map.zoom.maxLevel),i=1+k*j.options.map.zoom.step,g=j.mapConf.width/i,h=j.mapConf.height/i,0===k?(e=0,f=0):(c.fixedCenter!==d&&!0===c.fixedCenter?(e=j.zoomData.panX+(c.x-j.zoomData.panX)*(i-l)/i,f=j.zoomData.panY+(c.y-j.zoomData.panY)*(i-l)/i):(e=c.x-g/2,f=c.y-h/2),e=Math.min(Math.max(0,e),j.mapConf.width-g),f=Math.min(Math.max(0,f),j.mapConf.height-h)),i===l&&e===j.zoomData.panX&&f===j.zoomData.panY||(m>0?j.animateViewBox(e,f,g,h,m,j.options.map.zoom.animEasing):(j.setViewBox(e,f,g,h),clearTimeout(j.zoomTO),j.zoomTO=setTimeout(function(){j.$map.trigger("afterZoom",{x1:e,y1:f,x2:e+g,y2:f+h})},j.zoomFilteringTO)),a.extend(j.zoomData,{zoomLevel:k,panX:e,panY:f,zoomX:e+g/2,zoomY:f+h/2}))},onShowElementsInRange:function(a,b){var c=this;b.animDuration===d&&(b.animDuration=0),b.hiddenOpacity===d&&(b.hiddenOpacity=.3),b.ranges&&b.ranges.area&&c.showElemByRange(b.ranges.area,c.areas,b.hiddenOpacity,b.animDuration),b.ranges&&b.ranges.plot&&c.showElemByRange(b.ranges.plot,c.plots,b.hiddenOpacity,b.animDuration),b.ranges&&b.ranges.link&&c.showElemByRange(b.ranges.link,c.links,b.hiddenOpacity,b.animDuration),b.afterShowRange&&b.afterShowRange()},showElemByRange:function(b,c,e,f){var g=this,h={};b.min===d&&b.max===d||(b={0:b}),a.each(b,function(f){var g=b[f];if(g.min===d&&g.max===d)return!0;a.each(c,function(a){var b=c[a].options.value;if("object"!=typeof b&&(b=[b]),b[f]===d)return!0;g.min!==d&&b[f]<g.min||g.max!==d&&b[f]>g.max?h[a]=e:h[a]=1})}),a.each(h,function(a){g.setElementOpacity(c[a],h[a],f)})},setElementOpacity:function(a,b,c){var d=this;b>0&&(a.mapElem.show(),a.textElem&&a.textElem.show()),d.animate(a.mapElem,{opacity:b},c,function(){0===b&&a.mapElem.hide()}),d.animate(a.textElem,{opacity:b},c,function(){0===b&&a.textElem.hide()})},onUpdateEvent:function(b,c){var e=this;if("object"==typeof c){var f=0,g=c.animDuration?c.animDuration:0,h=function(a){e.animate(a.mapElem,{opacity:0},g,function(){a.mapElem.remove()}),e.animate(a.textElem,{opacity:0},g,function(){a.textElem.remove()})},i=function(a){a.mapElem.attr({opacity:0}),a.textElem&&a.textElem.attr({opacity:0}),e.setElementOpacity(a,a.mapElem.originalAttrs.opacity!==d?a.mapElem.originalAttrs.opacity:1,g)};if("object"==typeof c.mapOptions&&(!0===c.replaceOptions?e.options=e.extendDefaultOptions(c.mapOptions):a.extend(!0,e.options,c.mapOptions),c.mapOptions.areas===d&&c.mapOptions.plots===d&&c.mapOptions.legend===d||a("[data-type='legend-elem']",e.$container).each(function(b,c){"1"===a(c).attr("data-hidden")&&a(c).trigger("click",{hideOtherElems:!1,animDuration:g})})),"object"==typeof c.deletePlotKeys)for(;f<c.deletePlotKeys.length;f++)e.plots[c.deletePlotKeys[f]]!==d&&(h(e.plots[c.deletePlotKeys[f]]),delete e.plots[c.deletePlotKeys[f]]);else"all"===c.deletePlotKeys&&(a.each(e.plots,function(a,b){h(b)}),e.plots={});if("object"==typeof c.deleteLinkKeys)for(f=0;f<c.deleteLinkKeys.length;f++)e.links[c.deleteLinkKeys[f]]!==d&&(h(e.links[c.deleteLinkKeys[f]]),delete e.links[c.deleteLinkKeys[f]]);else"all"===c.deleteLinkKeys&&(a.each(e.links,function(a,b){h(b)}),e.links={});if("object"==typeof c.newPlots&&a.each(c.newPlots,function(a){e.plots[a]===d&&(e.options.plots[a]=c.newPlots[a],e.plots[a]=e.drawPlot(a),g>0&&i(e.plots[a]))}),"object"==typeof c.newLinks){var j=e.drawLinksCollection(c.newLinks);a.extend(e.links,j),a.extend(e.options.links,c.newLinks),g>0&&a.each(j,function(a){i(j[a])})}if(a.each(e.areas,function(a){("object"==typeof c.mapOptions&&("object"==typeof c.mapOptions.map&&"object"==typeof c.mapOptions.map.defaultArea||"object"==typeof c.mapOptions.areas&&"object"==typeof c.mapOptions.areas[a]||"object"==typeof c.mapOptions.legend&&"object"==typeof c.mapOptions.legend.area)||!0===c.replaceOptions)&&(e.areas[a].options=e.getElemOptions(e.options.map.defaultArea,e.options.areas[a]?e.options.areas[a]:{},e.options.legend.area),e.updateElem(e.areas[a],g))}),a.each(e.plots,function(a){("object"==typeof c.mapOptions&&("object"==typeof c.mapOptions.map&&"object"==typeof c.mapOptions.map.defaultPlot||"object"==typeof c.mapOptions.plots&&"object"==typeof c.mapOptions.plots[a]||"object"==typeof c.mapOptions.legend&&"object"==typeof c.mapOptions.legend.plot)||!0===c.replaceOptions)&&(e.plots[a].options=e.getElemOptions(e.options.map.defaultPlot,e.options.plots[a]?e.options.plots[a]:{},e.options.legend.plot),e.setPlotCoords(e.plots[a]),e.setPlotAttributes(e.plots[a]),e.updateElem(e.plots[a],g))}),a.each(e.links,function(a){("object"==typeof c.mapOptions&&("object"==typeof c.mapOptions.map&&"object"==typeof c.mapOptions.map.defaultLink||"object"==typeof c.mapOptions.links&&"object"==typeof c.mapOptions.links[a])||!0===c.replaceOptions)&&(e.links[a].options=e.getElemOptions(e.options.map.defaultLink,e.options.links[a]?e.options.links[a]:{},{}),e.updateElem(e.links[a],g))}),c.mapOptions&&("object"==typeof c.mapOptions.legend||"object"==typeof c.mapOptions.map&&"object"==typeof c.mapOptions.map.defaultArea||"object"==typeof c.mapOptions.map&&"object"==typeof c.mapOptions.map.defaultPlot)&&(a("[data-type='legend-elem']",e.$container).each(function(b,c){"1"===a(c).attr("data-hidden")&&a(c).trigger("click",{hideOtherElems:!1,animDuration:g})}),e.createLegends("area",e.areas,1),e.options.map.width?e.createLegends("plot",e.plots,e.options.map.width/e.mapConf.width):e.createLegends("plot",e.plots,e.$map.width()/e.mapConf.width)),"object"==typeof c.setLegendElemsState)a.each(c.setLegendElemsState,function(b,c){var f=e.$container.find("."+b)[0];f!==d&&a("[data-type='legend-elem']",f).each(function(b,d){("0"===a(d).attr("data-hidden")&&"hide"===c||"1"===a(d).attr("data-hidden")&&"show"===c)&&a(d).trigger("click",{hideOtherElems:!1,animDuration:g})})});else{var k="hide"===c.setLegendElemsState?"hide":"show";a("[data-type='legend-elem']",e.$container).each(function(b,c){("0"===a(c).attr("data-hidden")&&"hide"===k||"1"===a(c).attr("data-hidden")&&"show"===k)&&a(c).trigger("click",{hideOtherElems:!1,animDuration:g})})}e.initDelegatedCustomEvents(),c.afterUpdate&&c.afterUpdate(e.$container,e.paper,e.areas,e.plots,e.options,e.links)}},setPlotCoords:function(a){var b=this;if(a.options.x!==d&&a.options.y!==d)a.coords={x:a.options.x,y:a.options.y};else if(a.options.plotsOn!==d&&b.areas[a.options.plotsOn]!==d){var c=b.areas[a.options.plotsOn].mapElem.getBBox();a.coords={x:c.cx,y:c.cy}}else a.coords=b.mapConf.getCoords(a.options.latitude,a.options.longitude)},setPlotAttributes:function(a){"square"===a.options.type?(a.options.attrs.width=a.options.size,a.options.attrs.height=a.options.size,a.options.attrs.x=a.coords.x-a.options.size/2,a.options.attrs.y=a.coords.y-a.options.size/2):"image"===a.options.type?(a.options.attrs.src=a.options.url,a.options.attrs.width=a.options.width,a.options.attrs.height=a.options.height,a.options.attrs.x=a.coords.x-a.options.width/2,a.options.attrs.y=a.coords.y-a.options.height/2):"svg"===a.options.type?(a.options.attrs.path=a.options.path,a.options.attrs.transform===d&&(a.options.attrs.transform=""),a.mapElem.originalBBox===d&&(a.mapElem.originalBBox=a.mapElem.getBBox()),a.mapElem.baseTransform="m"+a.options.width/a.mapElem.originalBBox.width+",0,0,"+a.options.height/a.mapElem.originalBBox.height+","+(a.coords.x-a.options.width/2)+","+(a.coords.y-a.options.height/2),a.options.attrs.transform=a.mapElem.baseTransform+a.options.attrs.transform):(a.options.attrs.x=a.coords.x,a.options.attrs.y=a.coords.y,a.options.attrs.r=a.options.size/2)},drawLinksCollection:function(b){var c=this,e={},f={},g={},h={},i={};return a.each(b,function(a){var j=c.getElemOptions(c.options.map.defaultLink,b[a],{});if(e="string"==typeof b[a].between[0]?c.options.plots[b[a].between[0]]:b[a].between[0],f="string"==typeof b[a].between[1]?c.options.plots[b[a].between[1]]:b[a].between[1],e.plotsOn!==d&&c.areas[e.plotsOn]!==d){var k=c.areas[e.plotsOn].mapElem.getBBox();g={x:k.cx,y:k.cy}}else e.latitude!==d&&e.longitude!==d?g=c.mapConf.getCoords(e.latitude,e.longitude):(g.x=e.x,g.y=e.y);if(f.plotsOn!==d&&c.areas[f.plotsOn]!==d){var l=c.areas[f.plotsOn].mapElem.getBBox();h={x:l.cx,y:l.cy}}else f.latitude!==d&&f.longitude!==d?h=c.mapConf.getCoords(f.latitude,f.longitude):(h.x=f.x,h.y=f.y);i[a]=c.drawLink(a,g.x,g.y,h.x,h.y,j)}),i},drawLink:function(a,b,c,d,e,f){var g=this,h={options:f},i=(b+d)/2,j=(c+e)/2,k=-1/((e-c)/(d-b)),l=j-k*i,m=Math.sqrt((d-b)*(d-b)+(e-c)*(e-c)),n=1+k*k,o=-2*i+2*k*l-2*k*j,p=i*i+l*l-l*j-j*l+j*j-f.factor*m*(f.factor*m),q=o*o-4*n*p,r=0,s=0;return f.factor>0?(r=(-o+Math.sqrt(q))/(2*n),s=k*r+l):(r=(-o-Math.sqrt(q))/(2*n),s=k*r+l),h.mapElem=g.paper.path("m "+b+","+c+" C "+r+","+s+" "+d+","+e+" "+d+","+e),g.initElem(a,"link",h),h},isAttrsChanged:function(a,b){for(var c in b)if(b.hasOwnProperty(c)&&void 0===a[c]||b[c]!==a[c])return!0;return!1},updateElem:function(b,c){var e,f,g,h=this;if(!0===b.options.toFront&&b.mapElem.toFront(),b.options.href!==d?(b.options.attrs.cursor="pointer",b.options.text&&(b.options.text.attrs.cursor="pointer")):"pointer"===b.mapElem.attrs.cursor&&(b.options.attrs.cursor="auto",b.options.text&&(b.options.text.attrs.cursor="auto")),b.textElem){b.options.text.attrs.text=b.options.text.content,e=b.mapElem.getBBox(),(b.options.size||b.options.width&&b.options.height)&&("image"===b.options.type||"svg"===b.options.type?(f=(b.options.width-e.width)/2,g=(b.options.height-e.height)/2):(f=(b.options.size-e.width)/2,g=(b.options.size-e.height)/2),e.x-=f,e.x2+=f,e.y-=g,e.y2+=g);var i=h.getTextPosition(e,b.options.text.position,b.options.text.margin);b.options.text.attrs.x=i.x,b.options.text.attrs.y=i.y,b.options.text.attrs["text-anchor"]=i.textAnchor,h.setHoverOptions(b.textElem,b.options.text.attrs,b.options.text.attrsHover),h.isAttrsChanged(b.textElem.attrs,b.options.text.attrs)&&h.animate(b.textElem,b.options.text.attrs,c)}h.setHoverOptions(b.mapElem,b.options.attrs,b.options.attrsHover),h.isAttrsChanged(b.mapElem.attrs,b.options.attrs)&&h.animate(b.mapElem,b.options.attrs,c),b.options.cssClass!==d&&a(b.mapElem.node).removeClass().addClass(b.options.cssClass)},drawPlot:function(a){var b=this,c={};return c.options=b.getElemOptions(b.options.map.defaultPlot,b.options.plots[a]?b.options.plots[a]:{},b.options.legend.plot),b.setPlotCoords(c),"svg"===c.options.type&&(c.mapElem=b.paper.path(c.options.path)),b.setPlotAttributes(c),"square"===c.options.type?c.mapElem=b.paper.rect(c.options.attrs.x,c.options.attrs.y,c.options.attrs.width,c.options.attrs.height):"image"===c.options.type?c.mapElem=b.paper.image(c.options.attrs.src,c.options.attrs.x,c.options.attrs.y,c.options.attrs.width,c.options.attrs.height):"svg"===c.options.type||(c.mapElem=b.paper.circle(c.options.attrs.x,c.options.attrs.y,c.options.attrs.r)),b.initElem(a,"plot",c),c},setEventHandlers:function(b,c,e){var f=this;a.each(e.options.eventHandlers,function(a){f.customEventHandlers[a]===d&&(f.customEventHandlers[a]={}),f.customEventHandlers[a][c]===d&&(f.customEventHandlers[a][c]={}),f.customEventHandlers[a][c][b]=e})},drawLegend:function(c,e,f,g,h){var i=this,j={},k={},l=0,m=0,n=null,o=null,p={},q=0,r=0,s=0,t=0,u=[];j=a("."+c.cssClass,i.$container);var v=j.html();for(j.empty(),k=new b(j.get(0)),a(k.canvas).attr({"data-legend-type":e,"data-legend-id":h}),m=l=0,c.title&&""!==c.title&&(n=k.text(c.marginLeftTitle,0,c.title).attr(c.titleAttrs),o=n.getBBox(),n.attr({y:.5*o.height}),l=c.marginLeftTitle+o.width,m+=c.marginBottomTitle+o.height),q=0;q<c.slices.length;++q){var w=0;u[q]=a.extend(!0,{},"plot"===e?i.options.map.defaultPlot:i.options.map.defaultArea,c.slices[q]),c.slices[q].legendSpecificAttrs===d&&(c.slices[q].legendSpecificAttrs={}),a.extend(!0,u[q].attrs,c.slices[q].legendSpecificAttrs),"area"===e?(u[q].attrs.width===d&&(u[q].attrs.width=30),u[q].attrs.height===d&&(u[q].attrs.height=20)):"square"===u[q].type?(u[q].attrs.width===d&&(u[q].attrs.width=u[q].size),u[q].attrs.height===d&&(u[q].attrs.height=u[q].size)):"image"===u[q].type||"svg"===u[q].type?(u[q].attrs.width===d&&(u[q].attrs.width=u[q].width),u[q].attrs.height===d&&(u[q].attrs.height=u[q].height)):u[q].attrs.r===d&&(u[q].attrs.r=u[q].size/2),w=c.marginBottomTitle,n&&(w+=o.height),"plot"!==e||u[q].type!==d&&"circle"!==u[q].type?w+=g*u[q].attrs.height/2:w+=g*u[q].attrs.r,t=Math.max(t,w)}for("horizontal"===c.mode&&(l=c.marginLeft),q=0;q<u.length;++q){var x={},y={},z={};if(u[q].display===d||!0===u[q].display){if("area"===e?("horizontal"===c.mode?(r=l+c.marginLeft,s=t-.5*g*u[q].attrs.height):(r=c.marginLeft,s=m),x=k.rect(r,s,g*u[q].attrs.width,g*u[q].attrs.height)):"square"===u[q].type?("horizontal"===c.mode?(r=l+c.marginLeft,s=t-.5*g*u[q].attrs.height):(r=c.marginLeft,s=m),x=k.rect(r,s,g*u[q].attrs.width,g*u[q].attrs.height)):"image"===u[q].type||"svg"===u[q].type?("horizontal"===c.mode?(r=l+c.marginLeft,s=t-.5*g*u[q].attrs.height):(r=c.marginLeft,s=m),"image"===u[q].type?x=k.image(u[q].url,r,s,g*u[q].attrs.width,g*u[q].attrs.height):(x=k.path(u[q].path),u[q].attrs.transform===d&&(u[q].attrs.transform=""),y=x.getBBox(),u[q].attrs.transform="m"+g*u[q].width/y.width+",0,0,"+g*u[q].height/y.height+","+r+","+s+u[q].attrs.transform)):("horizontal"===c.mode?(r=l+c.marginLeft+g*u[q].attrs.r,s=t):(r=c.marginLeft+g*u[q].attrs.r,s=m+g*u[q].attrs.r),x=k.circle(r,s,g*u[q].attrs.r)),delete u[q].attrs.width,delete u[q].attrs.height,delete u[q].attrs.r,x.attr(u[q].attrs),y=x.getBBox(),"horizontal"===c.mode?(r=l+c.marginLeft+y.width+c.marginLeftLabel,s=t):(r=c.marginLeft+y.width+c.marginLeftLabel,s=m+y.height/2),z=k.text(r,s,u[q].label).attr(c.labelAttrs),"horizontal"===c.mode){var A=c.marginBottom+y.height;l+=c.marginLeft+y.width+c.marginLeftLabel+z.getBBox().width,"image"!==u[q].type&&"area"!==e&&(A+=c.marginBottomTitle),n&&(A+=o.height),m=Math.max(m,A)}else l=Math.max(l,c.marginLeft+y.width+c.marginLeftLabel+z.getBBox().width),m+=c.marginBottom+y.height;a(x.node).attr({"data-legend-id":h,"data-legend-type":e,"data-type":"legend-elem","data-id":q,"data-hidden":0}),a(z.node).attr({"data-legend-id":h,"data-legend-type":e,"data-type":"legend-label","data-id":q,"data-hidden":0}),p[q]={mapElem:x,textElem:z},c.hideElemsOnClick.enabled&&(z.attr({cursor:"pointer"}),x.attr({cursor:"pointer"}),i.setHoverOptions(x,u[q].attrs,u[q].attrs),i.setHoverOptions(z,c.labelAttrs,c.labelAttrsHover),u[q].clicked!==d&&!0===u[q].clicked&&i.handleClickOnLegendElem(p[q],q,h,e,{hideOtherElems:!1}))}}return"SVG"!==b.type&&c.VMLWidth&&(l=c.VMLWidth),k.setSize(l,m),{container:j,initialHTMLContent:v,elems:p}},handleClickOnLegendElem:function(b,c,e,f,g){var h,i=this;g=g||{},h=a.isArray(i.options.legend[f])?i.options.legend[f][e]:i.options.legend[f];var j=b.mapElem,k=b.textElem,l=a(j.node),m=a(k.node),n=h.slices[c],o="area"===f?i.areas:i.plots,p=g.animDuration!==d?g.animDuration:h.hideElemsOnClick.animDuration,q=l.attr("data-hidden"),r="0"===q?{"data-hidden":"1"}:{"data-hidden":"0"};"0"===q?i.animate(k,{opacity:.5},p):i.animate(k,{opacity:1},p),a.each(o,function(b){var c,f=o[b].mapElem.data("hidden-by");f===d&&(f={}),c=a.isArray(o[b].options.value)?o[b].options.value[e]:o[b].options.value,i.getLegendSlice(c,h)===n&&("0"===q?(f[e]=!0,i.setElementOpacity(o[b],h.hideElemsOnClick.opacity,p)):(delete f[e],a.isEmptyObject(f)&&i.setElementOpacity(o[b],o[b].mapElem.originalAttrs.opacity!==d?o[b].mapElem.originalAttrs.opacity:1,p)),o[b].mapElem.data("hidden-by",f))}),l.attr(r),m.attr(r),g.hideOtherElems!==d&&!0!==g.hideOtherElems||!0!==h.exclusive||a("[data-type='legend-elem'][data-hidden=0]",i.$container).each(function(){var b=a(this);b.attr("data-id")!==c&&b.trigger("click",{hideOtherElems:!1})})},createLegends:function(b,c,d){var e=this,f=e.options.legend[b];a.isArray(e.options.legend[b])||(f=[e.options.legend[b]]),e.legends[b]={};for(var g=0;g<f.length;++g)!0===f[g].display&&a.isArray(f[g].slices)&&f[g].slices.length>0&&""!==f[g].cssClass&&0!==a("."+f[g].cssClass,e.$container).length&&(e.legends[b][g]=e.drawLegend(f[g],b,c,d,g))},setHoverOptions:function(c,d,e){"SVG"!==b.type&&delete e.transform,c.attrsHover=e,c.attrsHover.transform?c.originalAttrs=a.extend({transform:"s1"},d):c.originalAttrs=d},elemEnter:function(a){var b=this;if(a!==d){if(a.mapElem!==d&&b.animate(a.mapElem,a.mapElem.attrsHover,a.mapElem.attrsHover.animDuration),a.textElem!==d&&b.animate(a.textElem,a.textElem.attrsHover,a.textElem.attrsHover.animDuration),a.options&&a.options.tooltip!==d){var c="";b.$tooltip.removeClass().addClass(b.options.map.tooltip.cssClass),a.options.tooltip.content!==d&&(c="function"==typeof a.options.tooltip.content?a.options.tooltip.content(a.mapElem):a.options.tooltip.content),a.options.tooltip.cssClass!==d&&b.$tooltip.addClass(a.options.tooltip.cssClass),b.$tooltip.html(c).css("display","block")}a.mapElem===d&&a.textElem===d||b.paper.safari&&b.paper.safari()}},elemHover:function(a,b){var c=this;if(a!==d&&a.options.tooltip!==d){var e=b.pageX,f=b.pageY,g=10,h=20;"object"==typeof a.options.tooltip.offset&&(void 0!==a.options.tooltip.offset.left&&(g=a.options.tooltip.offset.left),void 0!==a.options.tooltip.offset.top&&(h=a.options.tooltip.offset.top));var i={left:Math.min(c.$map.width()-c.$tooltip.outerWidth()-5,e-c.$map.offset().left+g),top:Math.min(c.$map.height()-c.$tooltip.outerHeight()-5,f-c.$map.offset().top+h)};"object"==typeof a.options.tooltip.overflow&&(!0===a.options.tooltip.overflow.right&&(i.left=e-c.$map.offset().left+10),!0===a.options.tooltip.overflow.bottom&&(i.top=f-c.$map.offset().top+20)),c.$tooltip.css(i)}},elemOut:function(a){var b=this;a!==d&&(a.mapElem!==d&&b.animate(a.mapElem,a.mapElem.originalAttrs,a.mapElem.attrsHover.animDuration),a.textElem!==d&&b.animate(a.textElem,a.textElem.originalAttrs,a.textElem.attrsHover.animDuration),a.options&&a.options.tooltip!==d&&b.$tooltip.css({display:"none",top:-1e3,left:-1e3}),a.mapElem===d&&a.textElem===d||b.paper.safari&&b.paper.safari())},elemClick:function(a){var b=this;a!==d&&(b.panning||a.options.href===d||window.open(a.options.href,a.options.target))},getElemOptions:function(b,c,e){var f=this,g=a.extend(!0,{},b,c);if(g.value!==d)if(a.isArray(e))for(var h=0;h<e.length;++h)g=a.extend(!0,{},g,f.getLegendSlice(g.value[h],e[h]));else g=a.extend(!0,{},g,f.getLegendSlice(g.value,e));return g},getTextPosition:function(a,b,c){var d=0,e=0,f="";switch("number"==typeof c&&(c="bottom"===b||"top"===b?{x:0,y:c}:"right"===b||"left"===b?{x:c,y:0}:{x:0,y:0}),b){case"bottom":d=(a.x+a.x2)/2+c.x,e=a.y2+c.y,f="middle";break;case"top":d=(a.x+a.x2)/2+c.x,e=a.y-c.y,f="middle";break;case"left":d=a.x-c.x,e=(a.y+a.y2)/2+c.y,f="end";break;case"right":d=a.x2+c.x,e=(a.y+a.y2)/2+c.y,f="start";break;default:d=(a.x+a.x2)/2+c.x,e=(a.y+a.y2)/2+c.y,f="middle"}return{x:d,y:e,textAnchor:f}},getLegendSlice:function(a,b){for(var c=0;c<b.slices.length;++c)if(b.slices[c].sliceValue!==d&&a===b.slices[c].sliceValue||b.slices[c].sliceValue===d&&(b.slices[c].min===d||a>=b.slices[c].min)&&(b.slices[c].max===d||a<=b.slices[c].max))return b.slices[c];return{}},animateViewBox:function(a,c,d,e,f,g){var h=this,i=h.currentViewBox.x,j=a-i,k=h.currentViewBox.y,l=c-k,m=h.currentViewBox.w,n=d-m,o=h.currentViewBox.h,p=e-o;h.zoomAnimCVBTarget||(h.zoomAnimCVBTarget={x:a,y:c,w:d,h:e});var q=m>d?"in":"out",r=b.easing_formulas[g||"linear"],s=f-2*f/100,t=h.zoomAnimStartTime;h.zoomAnimStartTime=(new Date).getTime();var u=function(){h.cancelAnimationFrame(h.zoomAnimID);var b=(new Date).getTime()-h.zoomAnimStartTime;if(b<s){var g,v,w,x;if(t&&h.zoomAnimCVBTarget&&h.zoomAnimCVBTarget.w!==d){var y=(new Date).getTime()-t,z=r(y/f);g=i+(h.zoomAnimCVBTarget.x-i)*z,v=k+(h.zoomAnimCVBTarget.y-k)*z,w=m+(h.zoomAnimCVBTarget.w-m)*z,x=o+(h.zoomAnimCVBTarget.h-o)*z,i=g,j=a-i,k=v,l=c-k,m=w,n=d-m,o=x,p=e-o,h.zoomAnimCVBTarget={x:a,y:c,w:d,h:e}}else{var A=r(b/f);g=i+j*A,v=k+l*A,w=m+n*A,x=o+p*A}"in"===q&&(w>h.currentViewBox.w||w<d)||"out"===q&&(w<h.currentViewBox.w||w>d)||h.setViewBox(g,v,w,x),h.zoomAnimID=h.requestAnimationFrame(u)}else h.zoomAnimStartTime=null,h.zoomAnimCVBTarget=null,h.currentViewBox.w!==d&&h.setViewBox(a,c,d,e),h.$map.trigger("afterZoom",{x1:a,y1:c,x2:a+d,y2:c+e})};u()},
requestAnimationFrame:function(a){return this._requestAnimationFrameFn.call(window,a)},cancelAnimationFrame:function(a){this._cancelAnimationFrameFn.call(window,a)},_requestAnimationFrameFn:function(){var a=function(){var b=(new Date).getTime();return function(c){var d=(new Date).getTime();if(!(d-b>16))return setTimeout(function(){a(c)},0);b=d,c(d)}}();return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||window.msRequestAnimationFrame||window.oRequestAnimationFrame||a}(),_cancelAnimationFrameFn:function(){return window.cancelAnimationFrame||window.webkitCancelAnimationFrame||window.webkitCancelRequestAnimationFrame||window.mozCancelAnimationFrame||window.mozCancelRequestAnimationFrame||window.msCancelAnimationFrame||window.msCancelRequestAnimationFrame||window.oCancelAnimationFrame||window.oCancelRequestAnimationFrame||clearTimeout}(),setViewBox:function(a,b,c,d){var e=this;e.currentViewBox.x=a,e.currentViewBox.y=b,e.currentViewBox.w=c,e.currentViewBox.h=d,e.paper.setViewBox(a,b,c,d,!1)},_nonAnimatedAttrs:["arrow-end","arrow-start","gradient","class","cursor","text-anchor","font","font-family","font-style","font-weight","letter-spacing","src","href","target","title","stroke-dasharray","stroke-linecap","stroke-linejoin","stroke-miterlimit"],animate:function(a,b,c,e){var f=this;if(a)if(c>0){for(var g={},h=0;h<f._nonAnimatedAttrs.length;h++){var i=f._nonAnimatedAttrs[h];b[i]!==d&&(g[i]=b[i])}a.attr(g),a.animate(b,c,"linear",function(){e&&e()})}else a.attr(b),e&&e()},isRaphaelBBoxBugPresent:function(){var a=this,b=a.paper.text(-50,-50,"TEST"),c=b.getBBox();return b.remove(),0===c.width&&0===c.height},defaultOptions:{map:{cssClass:"map",tooltip:{cssClass:"mapTooltip"},defaultArea:{attrs:{fill:"#343434",stroke:"#5d5d5d","stroke-width":1,"stroke-linejoin":"round"},attrsHover:{fill:"#f38a03",animDuration:300},text:{position:"inner",margin:10,attrs:{"font-size":15,fill:"#c7c7c7"},attrsHover:{fill:"#eaeaea",animDuration:300}},target:"_self",cssClass:"area"},defaultPlot:{type:"circle",size:15,attrs:{fill:"#0088db",stroke:"#fff","stroke-width":0,"stroke-linejoin":"round"},attrsHover:{"stroke-width":3,animDuration:300},text:{position:"right",margin:10,attrs:{"font-size":15,fill:"#c7c7c7"},attrsHover:{fill:"#eaeaea",animDuration:300}},target:"_self",cssClass:"plot"},defaultLink:{factor:.5,attrs:{stroke:"#0088db","stroke-width":2},attrsHover:{animDuration:300},text:{position:"inner",margin:10,attrs:{"font-size":15,fill:"#c7c7c7"},attrsHover:{fill:"#eaeaea",animDuration:300}},target:"_self",cssClass:"link"},zoom:{enabled:!1,minLevel:0,maxLevel:10,step:.25,mousewheel:!0,touch:!0,animDuration:200,animEasing:"linear",buttons:{reset:{cssClass:"zoomButton zoomReset",content:"&#8226;",title:"Reset zoom"},in:{cssClass:"zoomButton zoomIn",content:"+",title:"Zoom in"},out:{cssClass:"zoomButton zoomOut",content:"&#8722;",title:"Zoom out"}}}},legend:{redrawOnResize:!0,area:[],plot:[]},areas:{},plots:{},links:{}},legendDefaultOptions:{area:{cssClass:"areaLegend",display:!0,marginLeft:10,marginLeftTitle:5,marginBottomTitle:10,marginLeftLabel:10,marginBottom:10,titleAttrs:{"font-size":16,fill:"#343434","text-anchor":"start"},labelAttrs:{"font-size":12,fill:"#343434","text-anchor":"start"},labelAttrsHover:{fill:"#787878",animDuration:300},hideElemsOnClick:{enabled:!0,opacity:.2,animDuration:300},slices:[],mode:"vertical"},plot:{cssClass:"plotLegend",display:!0,marginLeft:10,marginLeftTitle:5,marginBottomTitle:10,marginLeftLabel:10,marginBottom:10,titleAttrs:{"font-size":16,fill:"#343434","text-anchor":"start"},labelAttrs:{"font-size":12,fill:"#343434","text-anchor":"start"},labelAttrsHover:{fill:"#787878",animDuration:300},hideElemsOnClick:{enabled:!0,opacity:.2,animDuration:300},slices:[],mode:"vertical"}}},e.version="2.2.0",a.mapael===d&&(a.mapael=e),a.fn.mapael=function(b){return this.each(function(){a.data(this,"mapael")&&a.data(this,"mapael").destroy(),a.data(this,"mapael",new e(this,b))})},e});